# -*- coding: utf-8 -*-
"""
变量监控模块
实现PLC变量的实时监控功能
"""

import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from PySide6.QtCore import QThread, Signal, QMutex, QMutexLocker

from plc_connection import PLCConnection
from data_formatter import DataFormatter
from data_logger import DataLogger


@dataclass
class MonitorVariable:
    """监控变量数据类"""
    name: str
    db: int
    offset: int
    var_type: str
    enabled: bool = True
    last_raw_data: Optional[bytearray] = None
    last_value: Optional[Any] = None
    last_update_time: float = 0.0
    error_count: int = 0


class VariableMonitor(QThread):
    """变量监控线程类"""
    
    # 信号定义
    variable_updated = Signal(str, bytearray, object, float)  # 变量名, 原始数据, 解析值, 时间戳
    monitor_error = Signal(str, str)  # 变量名, 错误信息
    monitor_status_changed = Signal(bool)  # 监控状态变化
    
    def __init__(self, plc_connection: PLCConnection):
        super().__init__()
        self.plc_connection = plc_connection
        self.variables: Dict[str, MonitorVariable] = {}
        self.monitoring = False
        self.interval = 1.0  # 监控间隔（秒）
        self.mutex = QMutex()
        self.data_logger = DataLogger()  # 数据记录器
        
    def add_variable(self, name: str, db: int, offset: int, var_type: str) -> bool:
        """添加监控变量
        
        Args:
            name: 变量名
            db: 数据块号
            offset: 偏移地址
            var_type: 变量类型
            
        Returns:
            bool: 添加是否成功
        """
        # 验证变量配置
        is_valid, error_msg = DataFormatter.validate_variable_config(db, offset, var_type)
        if not is_valid:
            self.monitor_error.emit(name, error_msg)
            return False
        
        with QMutexLocker(self.mutex):
            # 检查变量名是否已存在
            if name in self.variables:
                self.monitor_error.emit(name, "变量名已存在")
                return False
            
            # 创建监控变量
            variable = MonitorVariable(
                name=name,
                db=db,
                offset=offset,
                var_type=var_type.upper()
            )
            
            self.variables[name] = variable
            print(f"添加监控变量: {name} (DB{db}.{offset}, {var_type})")
            return True
    
    def remove_variable(self, name: str) -> bool:
        """移除监控变量
        
        Args:
            name: 变量名
            
        Returns:
            bool: 移除是否成功
        """
        with QMutexLocker(self.mutex):
            if name in self.variables:
                del self.variables[name]
                print(f"移除监控变量: {name}")
                return True
            return False
    
    def set_variable_enabled(self, name: str, enabled: bool) -> bool:
        """设置变量启用状态
        
        Args:
            name: 变量名
            enabled: 是否启用
            
        Returns:
            bool: 设置是否成功
        """
        with QMutexLocker(self.mutex):
            if name in self.variables:
                self.variables[name].enabled = enabled
                print(f"设置变量 {name} 启用状态: {enabled}")
                return True
            return False
    
    def get_variables(self) -> List[Dict]:
        """获取所有监控变量信息
        
        Returns:
            List[Dict]: 变量信息列表
        """
        with QMutexLocker(self.mutex):
            result = []
            for var in self.variables.values():
                result.append({
                    "name": var.name,
                    "db": var.db,
                    "offset": var.offset,
                    "type": var.var_type,
                    "enabled": var.enabled,
                    "last_update": var.last_update_time,
                    "error_count": var.error_count
                })
            return result
    
    def clear_variables(self):
        """清空所有监控变量"""
        with QMutexLocker(self.mutex):
            self.variables.clear()
            print("清空所有监控变量")
    
    def set_interval(self, interval: float):
        """设置监控间隔
        
        Args:
            interval: 监控间隔（秒）
        """
        if 0.1 <= interval <= 10.0:
            self.interval = interval
            print(f"设置监控间隔: {interval}秒")
        else:
            print(f"监控间隔超出范围 (0.1-10.0秒): {interval}")
    
    def start_monitoring(self):
        """开始监控"""
        if not self.monitoring and not self.isRunning():
            self.monitoring = True
            self.start()
            self.monitor_status_changed.emit(True)
            print("开始变量监控")
    
    def stop_monitoring(self):
        """停止监控"""
        if self.monitoring:
            self.monitoring = False
            self.monitor_status_changed.emit(False)
            print("停止变量监控")
            
            # 等待线程结束
            if self.isRunning():
                self.wait(3000)  # 等待最多3秒
    
    def run(self):
        """监控线程主循环"""
        print("监控线程开始运行")
        
        while self.monitoring:
            try:
                # 检查PLC连接状态
                if not self.plc_connection.is_connected():
                    print("PLC未连接，暂停监控")
                    time.sleep(1.0)
                    continue
                
                # 获取当前时间戳
                current_time = time.time()
                
                # 监控所有启用的变量
                with QMutexLocker(self.mutex):
                    variables_to_monitor = [
                        var for var in self.variables.values() 
                        if var.enabled
                    ]
                
                for variable in variables_to_monitor:
                    if not self.monitoring:
                        break
                    
                    try:
                        # 读取变量值
                        raw_data, parsed_value = self.plc_connection.read_variable(
                            variable.db, variable.offset, variable.var_type
                        )
                        
                        if raw_data is not None:
                            # 更新变量信息
                            with QMutexLocker(self.mutex):
                                variable.last_raw_data = raw_data
                                variable.last_value = parsed_value
                                variable.last_update_time = current_time
                                variable.error_count = 0
                            
                            # 记录数据
                            self.data_logger.add_record(
                                variable.name, variable.db, variable.offset,
                                variable.var_type, raw_data, parsed_value, current_time
                            )

                            # 发射更新信号
                            self.variable_updated.emit(
                                variable.name, raw_data, parsed_value, current_time
                            )
                        else:
                            # 读取失败
                            with QMutexLocker(self.mutex):
                                variable.error_count += 1
                            
                            # 记录错误
                            self.data_logger.add_error_record(
                                variable.name, f"读取失败 (错误次数: {variable.error_count})"
                            )

                            self.monitor_error.emit(
                                variable.name, f"读取失败 (错误次数: {variable.error_count})"
                            )
                    
                    except Exception as e:
                        with QMutexLocker(self.mutex):
                            variable.error_count += 1
                        
                        # 记录异常错误
                        self.data_logger.add_error_record(
                            variable.name, f"监控异常: {str(e)}"
                        )

                        self.monitor_error.emit(
                            variable.name, f"监控异常: {str(e)}"
                        )
                
                # 等待指定间隔
                time.sleep(self.interval)
                
            except Exception as e:
                print(f"监控循环异常: {e}")
                time.sleep(1.0)
        
        print("监控线程结束")

    def get_data_logger(self) -> DataLogger:
        """获取数据记录器"""
        return self.data_logger

    def set_data_logging_enabled(self, enabled: bool):
        """设置数据记录启用状态"""
        self.data_logger.set_enabled(enabled)

    def set_data_logging_interval(self, interval: float):
        """设置数据记录间隔"""
        self.data_logger.set_record_interval(interval)
    
    def get_monitor_status(self) -> Dict:
        """获取监控状态信息
        
        Returns:
            Dict: 监控状态信息
        """
        with QMutexLocker(self.mutex):
            total_vars = len(self.variables)
            enabled_vars = sum(1 for var in self.variables.values() if var.enabled)
            error_vars = sum(1 for var in self.variables.values() if var.error_count > 0)
            
            return {
                "monitoring": self.monitoring,
                "interval": self.interval,
                "total_variables": total_vars,
                "enabled_variables": enabled_vars,
                "error_variables": error_vars,
                "plc_connected": self.plc_connection.is_connected()
            }
