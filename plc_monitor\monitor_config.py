# -*- coding: utf-8 -*-
"""
监控配置管理模块
"""

import json
import os
from typing import Dict, List, Any


class MonitorConfig:
    """监控配置管理类"""
    
    def __init__(self, config_file: str = "monitor_config.json"):
        self.config_file = config_file
        self.default_config = {
            "connection": {
                "ip": "************",
                "rack": 0,
                "slot": 1,
                "auto_connect": False
            },
            "monitor": {
                "interval": 1.0,
                "auto_start": False,
                "variables": []
            },
            "ui": {
                "window_size": [1200, 800],
                "window_position": [100, 100],
                "table_column_widths": [120, 60, 80, 80, 150, 150, 120],
                "show_binary": False,
                "highlight_changes": True,
                "auto_scroll": True
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置，确保所有必要的键都存在
                return self._merge_config(self.default_config, config)
            except (json.JSONDecodeError, IOError) as e:
                print(f"加载配置文件失败: {e}")
                return self.default_config.copy()
        else:
            # 如果配置文件不存在，创建默认配置文件
            self.save_config(self.default_config)
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any] = None) -> bool:
        """保存配置文件"""
        if config is None:
            config = self.config
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            return True
        except IOError as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def _merge_config(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """合并用户配置和默认配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def get_connection_config(self) -> Dict[str, Any]:
        """获取连接配置"""
        return self.config.get("connection", {})
    
    def update_connection_config(self, **kwargs) -> None:
        """更新连接配置"""
        conn_config = self.config.setdefault("connection", {})
        conn_config.update(kwargs)
    
    def get_monitor_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return self.config.get("monitor", {})
    
    def update_monitor_config(self, **kwargs) -> None:
        """更新监控配置"""
        monitor_config = self.config.setdefault("monitor", {})
        monitor_config.update(kwargs)
    
    def get_ui_config(self) -> Dict[str, Any]:
        """获取UI配置"""
        return self.config.get("ui", {})
    
    def update_ui_config(self, **kwargs) -> None:
        """更新UI配置"""
        ui_config = self.config.setdefault("ui", {})
        ui_config.update(kwargs)
    
    def get_monitor_variables(self) -> List[Dict[str, Any]]:
        """获取监控变量配置"""
        return self.config.get("monitor", {}).get("variables", [])
    
    def set_monitor_variables(self, variables: List[Dict[str, Any]]) -> None:
        """设置监控变量配置"""
        monitor_config = self.config.setdefault("monitor", {})
        monitor_config["variables"] = variables
    
    def add_monitor_variable(self, name: str, db: int, offset: int, var_type: str, enabled: bool = True) -> None:
        """添加监控变量"""
        variables = self.get_monitor_variables()
        
        # 检查是否已存在
        for var in variables:
            if var.get("name") == name:
                # 更新现有变量
                var.update({
                    "db": db,
                    "offset": offset,
                    "type": var_type,
                    "enabled": enabled
                })
                self.set_monitor_variables(variables)
                return
        
        # 添加新变量
        variables.append({
            "name": name,
            "db": db,
            "offset": offset,
            "type": var_type,
            "enabled": enabled
        })
        self.set_monitor_variables(variables)
    
    def remove_monitor_variable(self, name: str) -> bool:
        """移除监控变量"""
        variables = self.get_monitor_variables()
        original_count = len(variables)
        
        variables = [var for var in variables if var.get("name") != name]
        
        if len(variables) < original_count:
            self.set_monitor_variables(variables)
            return True
        return False
    
    def export_config(self, file_path: str) -> bool:
        """导出配置到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path: str) -> bool:
        """从文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 合并导入的配置
            self.config = self._merge_config(self.default_config, imported_config)
            return True
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self.config = self.default_config.copy()
    
    def validate_config(self) -> tuple[bool, List[str]]:
        """验证配置有效性"""
        errors = []
        
        # 验证连接配置
        conn_config = self.get_connection_config()
        ip = conn_config.get("ip", "")
        if not ip:
            errors.append("IP地址不能为空")
        
        rack = conn_config.get("rack", 0)
        if not (0 <= rack <= 7):
            errors.append("Rack必须在0-7之间")
        
        slot = conn_config.get("slot", 1)
        if not (0 <= slot <= 31):
            errors.append("Slot必须在0-31之间")
        
        # 验证监控配置
        monitor_config = self.get_monitor_config()
        interval = monitor_config.get("interval", 1.0)
        if not (0.1 <= interval <= 10.0):
            errors.append("监控间隔必须在0.1-10.0秒之间")
        
        # 验证监控变量
        variables = self.get_monitor_variables()
        for i, var in enumerate(variables):
            var_name = var.get("name", "")
            if not var_name:
                errors.append(f"第{i+1}个变量名不能为空")
            
            db = var.get("db", 0)
            if not (1 <= db <= 999):
                errors.append(f"变量'{var_name}'的DB号必须在1-999之间")
            
            offset = var.get("offset", 0)
            if offset < 0:
                errors.append(f"变量'{var_name}'的偏移地址不能为负数")
            
            var_type = var.get("type", "")
            valid_types = ["BOOL", "BYTE", "INT", "DINT", "REAL"]
            if var_type.upper() not in valid_types:
                errors.append(f"变量'{var_name}'的类型无效，支持的类型: {', '.join(valid_types)}")
        
        return len(errors) == 0, errors
