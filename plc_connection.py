# -*- coding: utf-8 -*-
"""
PLC连接管理模块
基于python-snap7实现西门子S7 PLC连接功能
"""

import snap7
import struct
import time
from typing import Optional, Any, Tuple
from PySide6.QtCore import QObject, Signal


class PLCConnection(QObject):
    """PLC连接管理类"""
    
    # 信号定义
    connection_changed = Signal(bool)  # 连接状态变化
    error_occurred = Signal(str)  # 错误信号
    
    def __init__(self):
        super().__init__()
        self.client = snap7.client.Client()
        self.connected = False
        self.connection_params = {}
    
    def connect(self, ip: str, rack: int = 0, slot: int = 1) -> bool:
        """连接到PLC
        
        Args:
            ip: PLC IP地址
            rack: 机架号
            slot: 插槽号
            
        Returns:
            bool: 连接是否成功
        """
        try:
            print(f"正在连接PLC: {ip}, Rack: {rack}, Slot: {slot}")
            
            # 断开现有连接
            if self.connected:
                self.disconnect()
            
            # 连接PLC
            self.client.connect(ip, rack, slot)
            
            # 检查连接状态
            if self.client.get_connected():
                self.connected = True
                self.connection_params = {
                    "ip": ip,
                    "rack": rack,
                    "slot": slot
                }
                self.connection_changed.emit(True)
                print("PLC连接成功")
                
                # 获取PLC信息
                try:
                    cpu_info = self.client.get_cpu_info()
                    print(f"PLC信息: {cpu_info}")
                except:
                    print("无法获取PLC详细信息")
                
                return True
            else:
                self.connected = False
                self.connection_changed.emit(False)
                self.error_occurred.emit("PLC连接失败")
                return False
                
        except Exception as e:
            self.connected = False
            self.connection_changed.emit(False)
            error_msg = f"连接PLC时发生错误: {str(e)}"
            print(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def disconnect(self) -> None:
        """断开PLC连接"""
        try:
            if self.client.get_connected():
                self.client.disconnect()
            self.connected = False
            self.connection_params = {}
            self.connection_changed.emit(False)
            print("PLC连接已断开")
        except Exception as e:
            error_msg = f"断开PLC连接时发生错误: {str(e)}"
            print(error_msg)
            self.error_occurred.emit(error_msg)
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        try:
            return self.client.get_connected() if self.client else False
        except:
            return False
    
    def read_db_bytes(self, db_number: int, offset: int, size: int) -> Optional[bytearray]:
        """读取数据块字节数据
        
        Args:
            db_number: 数据块号
            offset: 偏移地址
            size: 读取字节数
            
        Returns:
            Optional[bytearray]: 读取的字节数据，失败返回None
        """
        try:
            if not self.is_connected():
                self.error_occurred.emit("PLC未连接")
                return None
            
            data = self.client.db_read(db_number, offset, size)
            return data
        except Exception as e:
            error_msg = f"读取DB{db_number}.{offset}[{size}]失败: {str(e)}"
            print(error_msg)
            self.error_occurred.emit(error_msg)
            return None
    
    def read_variable(self, db_number: int, offset: int, var_type: str) -> Tuple[Optional[bytearray], Optional[Any]]:
        """读取变量值，返回原始字节和解析后的值
        
        Args:
            db_number: 数据块号
            offset: 偏移地址
            var_type: 变量类型 (BOOL, BYTE, INT, DINT, REAL)
            
        Returns:
            Tuple[Optional[bytearray], Optional[Any]]: (原始字节数据, 解析后的值)
        """
        # 确定读取字节数
        size_map = {
            "BOOL": 1,
            "BYTE": 1,
            "INT": 2,
            "DINT": 4,
            "REAL": 4
        }
        
        size = size_map.get(var_type.upper(), 1)
        raw_data = self.read_db_bytes(db_number, offset, size)
        
        if raw_data is None:
            return None, None
        
        # 解析数据
        parsed_value = self._parse_data(raw_data, var_type)
        
        return raw_data, parsed_value
    
    def _parse_data(self, data: bytearray, var_type: str) -> Optional[Any]:
        """解析字节数据为对应类型的值"""
        try:
            var_type = var_type.upper()
            
            if var_type == "BOOL":
                # 布尔值取第一个字节的第0位
                return bool(data[0] & 0x01)
            
            elif var_type == "BYTE":
                return data[0]
            
            elif var_type == "INT":
                # 16位有符号整数，大端序
                return struct.unpack(">h", data[:2])[0]
            
            elif var_type == "DINT":
                # 32位有符号整数，大端序
                return struct.unpack(">i", data[:4])[0]
            
            elif var_type == "REAL":
                # 32位浮点数，大端序
                return struct.unpack(">f", data[:4])[0]
            
            else:
                self.error_occurred.emit(f"不支持的数据类型: {var_type}")
                return None
                
        except Exception as e:
            self.error_occurred.emit(f"解析数据失败: {str(e)}")
            return None
    
    def get_connection_info(self) -> dict:
        """获取连接信息"""
        return {
            "connected": self.connected,
            "params": self.connection_params.copy()
        }
    
    def __del__(self):
        """析构函数"""
        try:
            self.disconnect()
        except:
            pass
