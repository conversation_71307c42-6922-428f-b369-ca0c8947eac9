# PLC变量监控器依赖库
# PLC Variable Monitor Dependencies

# PySide6 - Qt6 GUI框架
PySide6>=6.5.0

# python-snap7 - Siemens S7 PLC通信库
python-snap7>=1.3

# 可选依赖 (Optional Dependencies)
# 如果需要更好的性能和功能，可以安装以下库

# numpy - 数值计算库，用于数据处理
# numpy>=1.21.0

# pandas - 数据分析库，用于数据导出
# pandas>=1.3.0

# matplotlib - 绘图库，用于数据可视化
# matplotlib>=3.5.0

# 安装说明:
# 1. 基本安装: pip install -r requirements_monitor.txt
# 2. 完整安装: 取消注释可选依赖后再安装
# 3. 开发环境: pip install -r requirements_monitor.txt --upgrade

# 注意事项:
# - 确保系统已安装Snap7库的C/C++组件
# - Windows用户可能需要安装Visual C++ Redistributable
# - Linux用户可能需要安装libsnap7-dev包
# - macOS用户可以通过brew安装snap7
