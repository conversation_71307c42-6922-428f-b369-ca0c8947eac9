# 西门子S7 PLC客户端测试程序

基于Python + PySide6 + python-snap7框架开发的西门子S7 PLC客户端测试工具。

## 功能特性

- **PLC连接管理**: 支持TCP/IP连接到西门子S7系列PLC
- **实时数据监控**: 可配置监控间隔，实时显示PLC变量值
- **数据读写操作**: 支持多种数据类型的读写操作
- **图形化界面**: 基于PySide6的现代化用户界面
- **配置管理**: JSON格式的配置文件，支持变量和连接参数配置
- **日志记录**: 实时显示操作日志和错误信息
- **PLC结构发现**: 自动扫描和发现PLC中的数据块和变量
- **配置导出**: 将发现的变量自动生成配置文件

## 支持的数据类型

- **BOOL**: 布尔值（位操作）
- **BYTE**: 字节值（0-255）
- **INT**: 16位有符号整数
- **DINT**: 32位有符号整数
- **REAL**: 32位浮点数
- **STRING**: 字符串（支持UTF-8编码）

## 系统要求

- Python 3.7+
- Windows/Linux/macOS
- 网络连接到PLC

## 安装依赖

```bash
pip install -r requirements.txt
```

或手动安装：

```bash
pip install PySide6 python-snap7
```

## 快速开始

### 1. 启动图形界面

```bash
python main.py
```

### 2. 配置PLC连接

在左侧控制面板中配置：
- **IP地址**: PLC的IP地址（如：************）
- **Rack**: 机架号（通常为0）
- **Slot**: 插槽号（CPU插槽，通常为1）

### 3. 连接PLC

点击"连接"按钮建立与PLC的连接。

### 4. 监控数据

连接成功后：
- 设置监控间隔（秒）
- 点击"开始监控"按钮
- 在"实时数据"标签页查看变量值

### 5. 写入数据

在"变量写入"区域：
- 选择要写入的变量
- 输入新值
- 点击"写入"按钮

### 6. 发现PLC结构

如果不知道PLC中的具体变量地址，可以使用结构发现功能：

1. **设置扫描范围**: 在"PLC结构发现"区域设置要扫描的DB范围（如DB1到DB50）
2. **开始发现**: 点击"发现PLC结构"按钮
3. **查看结果**: 在"PLC结构发现"标签页查看发现的变量
4. **导出配置**: 点击"导出配置"按钮将发现的变量保存为配置文件

### 7. 使用发现的变量

发现完成后：
- 查看发现摘要了解PLC结构
- 从发现结果中选择需要的变量
- 将变量配置复制到主配置文件中
- 重新加载程序使用新的变量配置

## 配置文件

程序首次运行时会自动创建`s7_config.json`配置文件，包含：

### PLC连接配置
```json
{
  "plc": {
    "ip": "************",
    "rack": 0,
    "slot": 1,
    "timeout": 5000,
    "connection_type": "PG"
  }
}
```

### 变量配置
```json
{
  "variables": {
    "temperature": {
      "db": 1,
      "offset": 0,
      "type": "REAL",
      "description": "温度值"
    },
    "motor_status": {
      "db": 1,
      "offset": 8,
      "type": "BOOL",
      "bit": 0,
      "description": "电机状态"
    }
  }
}
```

## 测试程序

运行测试脚本验证功能：

```bash
# 基本功能测试
python test_s7_client.py

# PLC结构发现测试
python test_plc_discovery.py
```

测试内容包括：
- 配置加载测试
- PLC连接测试
- 数据读写测试
- 监控功能测试
- PLC结构发现测试
- 变量自动识别测试

## 文件结构

```
├── main.py                    # 主程序入口
├── main_window.py             # 主窗口UI类
├── s7_client.py               # S7客户端连接类
├── config.py                  # 配置管理模块
├── test_s7_client.py          # 基本功能测试脚本
├── test_plc_discovery.py      # PLC结构发现测试脚本
├── requirements.txt           # 依赖列表
├── README.md                 # 说明文档
├── run.bat                   # Windows启动脚本
└── s7_config.json            # 配置文件（自动生成）
```

## 使用说明

### 变量配置格式

每个变量需要配置以下参数：

- **db**: 数据块号
- **offset**: 字节偏移量
- **type**: 数据类型（BOOL/BYTE/INT/DINT/REAL/STRING）
- **bit**: 位号（仅BOOL类型需要）
- **description**: 变量描述

### 数据类型说明

| 类型 | 大小 | 范围 | 说明 |
|------|------|------|------|
| BOOL | 1位 | True/False | 布尔值，需指定bit位 |
| BYTE | 1字节 | 0-255 | 无符号字节 |
| INT | 2字节 | -32768~32767 | 有符号短整型 |
| DINT | 4字节 | -2147483648~2147483647 | 有符号整型 |
| REAL | 4字节 | IEEE 754 | 32位浮点数 |
| STRING | 可变 | UTF-8字符串 | 字符串类型 |

## PLC结构发现功能详解

### 功能原理

当你不知道PLC中具体有哪些变量和地址时，可以使用结构发现功能：

1. **数据块扫描**: 程序会尝试读取指定范围内的所有数据块（DB1-DBn）
2. **大小检测**: 使用二分法自动检测每个数据块的实际大小
3. **数据分析**: 分析数据块内容，识别可能的变量类型和值
4. **结果展示**: 在表格中显示发现的所有可能变量

### 发现的变量类型

- **BOOL**: 检测0x00/0x01字节，为每个位生成布尔变量
- **INT**: 检测2字节有符号整数（-32768~32767）
- **DINT**: 检测4字节有符号整数
- **REAL**: 检测4字节IEEE 754浮点数
- **STRING**: 检测西门子字符串格式（长度+内容）

### 使用建议

1. **扫描范围**: 建议从小范围开始（如DB1-DB10），避免扫描过多无用数据块
2. **结果筛选**: 发现结果可能包含大量变量，需要根据实际需要筛选
3. **变量命名**: 自动生成的变量名包含DB号和偏移，可根据实际用途重命名
4. **类型验证**: 建议对重要变量进行手动验证，确保类型识别正确

### 导出和使用

1. 发现完成后点击"导出配置"保存结果
2. 编辑导出的JSON文件，修改变量名和描述
3. 将需要的变量复制到主配置文件`s7_config.json`
4. 重启程序加载新的变量配置

## 故障排除

### 连接失败
1. 检查PLC是否开机并连接到网络
2. 验证IP地址、Rack、Slot参数是否正确
3. 检查防火墙设置
4. 确认PLC允许外部连接

### 数据读写失败
1. 检查变量配置是否正确
2. 验证数据块是否存在
3. 确认偏移量和数据类型匹配

### 监控异常
1. 检查网络连接稳定性
2. 适当调整监控间隔
3. 查看日志信息定位问题

### 结构发现问题
1. **发现结果为空**: 检查DB范围设置，PLC可能使用不同的DB号
2. **发现速度慢**: 减小扫描范围或最大DB大小
3. **变量识别错误**: 这是正常现象，需要手动验证重要变量
4. **权限不足**: 某些PLC可能限制数据块访问权限

## 开发说明

### 添加新的数据类型
在`s7_client.py`中的`_parse_data`和`_value_to_bytes`方法中添加新类型的处理逻辑。

### 自定义UI
修改`main_window.py`中的界面布局和控件。

### 扩展配置
在`config.py`中添加新的配置项和管理方法。

## 许可证

本项目仅供学习和测试使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件

---

**注意**: 请确保在安全的测试环境中使用本工具，避免对生产系统造成影响。
