# -*- coding: utf-8 -*-
"""
数据记录器模块
实现实时监测数据的记录和保存功能
"""

import os
import csv
import json
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from PySide6.QtCore import QObject, Signal


@dataclass
class DataRecord:
    """数据记录数据类"""
    timestamp: float
    datetime_str: str
    variable_name: str
    db_number: int
    offset: int
    data_type: str
    raw_data: str  # 十六进制字符串
    decimal_value: str
    status: str = "正常"  # 正常、错误、超时等


class DataLogger(QObject):
    """数据记录器类"""
    
    # 信号定义
    record_added = Signal(dict)  # 新记录添加
    export_completed = Signal(str, bool)  # 导出完成，文件路径，是否成功
    
    def __init__(self):
        super().__init__()
        self.records: List[DataRecord] = []
        self.max_records = 10000  # 最大记录数
        self.auto_save_interval = 300  # 自动保存间隔（秒）
        self.last_auto_save = time.time()
        self.enabled = False
        self.record_interval = 1.0  # 记录间隔（秒）
        self.last_record_time = {}  # 每个变量的最后记录时间
        
    def set_enabled(self, enabled: bool):
        """设置记录器启用状态"""
        self.enabled = enabled
        if enabled:
            print(f"数据记录器已启用，最大记录数: {self.max_records}")
        else:
            print("数据记录器已禁用")
    
    def set_max_records(self, max_records: int):
        """设置最大记录数"""
        self.max_records = max_records
        self._trim_records()
    
    def set_record_interval(self, interval: float):
        """设置记录间隔"""
        self.record_interval = interval
    
    def should_record(self, variable_name: str) -> bool:
        """判断是否应该记录该变量"""
        if not self.enabled:
            return False
            
        current_time = time.time()
        last_time = self.last_record_time.get(variable_name, 0)
        
        return (current_time - last_time) >= self.record_interval
    
    def add_record(self, variable_name: str, db_number: int, offset: int, 
                   data_type: str, raw_data: bytearray, decimal_value: Any,
                   timestamp: float = None, status: str = "正常"):
        """添加数据记录"""
        if not self.enabled:
            return
            
        if not self.should_record(variable_name):
            return
            
        if timestamp is None:
            timestamp = time.time()
            
        # 更新最后记录时间
        self.last_record_time[variable_name] = timestamp
        
        # 创建记录
        record = DataRecord(
            timestamp=timestamp,
            datetime_str=datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
            variable_name=variable_name,
            db_number=db_number,
            offset=offset,
            data_type=data_type,
            raw_data=' '.join(f'{b:02X}' for b in raw_data),
            decimal_value=str(decimal_value),
            status=status
        )
        
        self.records.append(record)
        self._trim_records()
        
        # 发送信号
        self.record_added.emit(asdict(record))
        
        # 检查自动保存
        self._check_auto_save()
        
        print(f"记录数据: {variable_name} = {decimal_value} (总记录数: {len(self.records)})")
    
    def add_error_record(self, variable_name: str, error_message: str):
        """添加错误记录"""
        if not self.enabled:
            return
            
        timestamp = time.time()
        record = DataRecord(
            timestamp=timestamp,
            datetime_str=datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
            variable_name=variable_name,
            db_number=0,
            offset=0,
            data_type="ERROR",
            raw_data="",
            decimal_value=error_message,
            status="错误"
        )
        
        self.records.append(record)
        self._trim_records()
        self.record_added.emit(asdict(record))
    
    def _trim_records(self):
        """修剪记录，保持在最大记录数内"""
        if len(self.records) > self.max_records:
            excess = len(self.records) - self.max_records
            self.records = self.records[excess:]
            print(f"记录数超限，删除了 {excess} 条旧记录")
    
    def _check_auto_save(self):
        """检查是否需要自动保存"""
        current_time = time.time()
        if current_time - self.last_auto_save >= self.auto_save_interval:
            self.auto_save()
            self.last_auto_save = current_time
    
    def auto_save(self):
        """自动保存到临时文件"""
        if not self.records:
            return
            
        try:
            filename = f"data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            self.export_to_csv(filename)
            print(f"自动保存完成: {filename}")
        except Exception as e:
            print(f"自动保存失败: {e}")
    
    def get_records(self, limit: int = None) -> List[Dict[str, Any]]:
        """获取记录列表"""
        records = self.records[-limit:] if limit else self.records
        return [asdict(record) for record in records]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.records:
            return {
                "total_records": 0,
                "variables": [],
                "time_range": None,
                "error_count": 0
            }
        
        variables = list(set(record.variable_name for record in self.records))
        error_count = sum(1 for record in self.records if record.status == "错误")
        
        return {
            "total_records": len(self.records),
            "variables": variables,
            "time_range": {
                "start": self.records[0].datetime_str,
                "end": self.records[-1].datetime_str
            },
            "error_count": error_count,
            "enabled": self.enabled,
            "max_records": self.max_records
        }
    
    def clear_records(self):
        """清空所有记录"""
        self.records.clear()
        self.last_record_time.clear()
        print("已清空所有数据记录")
    
    def export_to_csv(self, filename: str) -> bool:
        """导出到CSV文件"""
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                if not self.records:
                    # 写入空文件头
                    writer = csv.writer(csvfile)
                    writer.writerow(['时间戳', '日期时间', '变量名', 'DB号', '偏移', '数据类型', '原始数据', '十进制值', '状态'])
                    self.export_completed.emit(filename, True)
                    return True
                
                fieldnames = ['timestamp', 'datetime_str', 'variable_name', 'db_number', 
                             'offset', 'data_type', 'raw_data', 'decimal_value', 'status']
                
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                # 写入中文表头
                writer.writerow({
                    'timestamp': '时间戳',
                    'datetime_str': '日期时间',
                    'variable_name': '变量名',
                    'db_number': 'DB号',
                    'offset': '偏移',
                    'data_type': '数据类型',
                    'raw_data': '原始数据',
                    'decimal_value': '十进制值',
                    'status': '状态'
                })
                
                # 写入数据
                for record in self.records:
                    writer.writerow(asdict(record))
            
            self.export_completed.emit(filename, True)
            print(f"CSV导出成功: {filename}")
            return True
            
        except Exception as e:
            print(f"CSV导出失败: {e}")
            self.export_completed.emit(filename, False)
            return False
    
    def export_to_json(self, filename: str) -> bool:
        """导出到JSON文件"""
        try:
            data = {
                "export_time": datetime.now().isoformat(),
                "statistics": self.get_statistics(),
                "records": self.get_records()
            }
            
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, ensure_ascii=False, indent=2)
            
            self.export_completed.emit(filename, True)
            print(f"JSON导出成功: {filename}")
            return True
            
        except Exception as e:
            print(f"JSON导出失败: {e}")
            self.export_completed.emit(filename, False)
            return False
    
    def import_from_csv(self, filename: str) -> bool:
        """从CSV文件导入"""
        try:
            with open(filename, 'r', encoding='utf-8-sig') as csvfile:
                reader = csv.DictReader(csvfile)
                
                imported_count = 0
                for row in reader:
                    # 跳过表头行
                    if row.get('timestamp') == '时间戳':
                        continue
                        
                    try:
                        record = DataRecord(
                            timestamp=float(row['timestamp']),
                            datetime_str=row['datetime_str'],
                            variable_name=row['variable_name'],
                            db_number=int(row['db_number']),
                            offset=int(row['offset']),
                            data_type=row['data_type'],
                            raw_data=row['raw_data'],
                            decimal_value=row['decimal_value'],
                            status=row.get('status', '正常')
                        )
                        self.records.append(record)
                        imported_count += 1
                    except (ValueError, KeyError) as e:
                        print(f"跳过无效记录: {e}")
                        continue
                
                self._trim_records()
                print(f"CSV导入成功: {imported_count} 条记录")
                return True
                
        except Exception as e:
            print(f"CSV导入失败: {e}")
            return False
