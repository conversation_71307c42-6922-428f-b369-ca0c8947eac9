#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
下载snap7动态链接库
"""

import os
import urllib.request
import zipfile
import shutil

def download_snap7_dll():
    """下载snap7.dll文件"""
    print("正在下载snap7动态链接库...")
    
    # snap7 DLL下载地址（Windows 64位）
    dll_url = "https://github.com/gijzelaerr/python-snap7/raw/master/snap7/lib/snap7.dll"
    
    try:
        # 创建lib目录
        lib_dir = "snap7_lib"
        os.makedirs(lib_dir, exist_ok=True)
        
        # 下载DLL文件
        dll_path = os.path.join(lib_dir, "snap7.dll")
        print(f"下载到: {dll_path}")
        
        urllib.request.urlretrieve(dll_url, dll_path)
        
        if os.path.exists(dll_path):
            print("✅ snap7.dll下载成功！")
            print(f"文件大小: {os.path.getsize(dll_path)} 字节")
            return dll_path
        else:
            print("❌ 下载失败")
            return None
            
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        
        # 尝试从python-snap7包中复制
        try:
            import snap7
            snap7_path = os.path.dirname(snap7.__file__)
            source_dll = os.path.join(snap7_path, "lib", "snap7.dll")
            
            if os.path.exists(source_dll):
                print("从python-snap7包中复制DLL文件...")
                shutil.copy2(source_dll, dll_path)
                print("✅ 从本地复制成功！")
                return dll_path
            else:
                print("❌ 本地也未找到snap7.dll")
                return None
                
        except Exception as e2:
            print(f"❌ 本地复制也失败: {e2}")
            return None

if __name__ == "__main__":
    download_snap7_dll()
