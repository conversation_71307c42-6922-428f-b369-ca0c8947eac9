# PLC变量监控器

一个专注于Siemens S7 PLC变量实时监控的Python应用程序。

## 功能特性

### 核心功能
1. **PLC连接管理** - 与原程序相同的连接功能
2. **实时变量监控** - 对指定数据块和变量进行实时监测
3. **双值显示** - 同时显示原始值和十进制值
4. **可配置监控间隔** - 支持设置监听间隔（0.1-10.0秒）

### 增强功能
- 变量配置管理（添加、删除、启用/禁用）
- 数据变化高亮显示
- 配置文件导入/导出
- 数据导出（CSV/JSON格式）
- 实时统计信息显示
- 用户友好的图形界面

## 支持的数据类型

| 类型 | 大小 | 描述 | 取值范围 |
|------|------|------|----------|
| BOOL | 1字节 | 布尔值 | True/False |
| BYTE | 1字节 | 8位无符号整数 | 0-255 |
| INT | 2字节 | 16位有符号整数 | -32,768 - 32,767 |
| DINT | 4字节 | 32位有符号整数 | -2,147,483,648 - 2,147,483,647 |
| REAL | 4字节 | 32位IEEE 754浮点数 | ±1.175e-38 - ±3.403e+38 |

## 安装说明

### 1. 环境要求
- Python 3.8+
- Windows/Linux/macOS

### 2. 安装依赖
```bash
pip install -r requirements_monitor.txt
```

### 3. 系统依赖
- **Windows**: 可能需要Visual C++ Redistributable
- **Linux**: 需要安装libsnap7-dev
- **macOS**: 可通过brew安装snap7

## 使用指南

### 1. 启动程序
```bash
python main_monitor.py
```

### 2. 连接PLC
1. 在"PLC连接"区域输入PLC参数：
   - IP地址（如：************）
   - Rack（通常为0）
   - Slot（通常为1）
2. 点击"连接"按钮

### 3. 添加监控变量
1. 点击"添加变量"按钮
2. 填写变量信息：
   - 变量名：自定义名称
   - 数据块(DB)：1-999
   - 偏移地址：字节偏移
   - 数据类型：BOOL/BYTE/INT/DINT/REAL
   - 启用监控：是否启用
3. 点击"确定"

### 4. 开始监控
1. 确保PLC已连接
2. 设置监控间隔（0.1-10.0秒）
3. 点击"开始监控"按钮
4. 观察实时数据更新

### 5. 数据显示
- **原始值**：十六进制字节数据
- **十进制值**：解析后的数值
- **变化指示**：↑上升 ↓下降 =不变 ≠改变
- **更新时间**：最后更新的时间戳

## 配置管理

### 配置文件结构
```json
{
    "connection": {
        "ip": "************",
        "rack": 0,
        "slot": 1,
        "auto_connect": false
    },
    "monitor": {
        "interval": 1.0,
        "auto_start": false,
        "variables": [...]
    },
    "ui": {
        "window_size": [1200, 800],
        "show_binary": false,
        "highlight_changes": true,
        "auto_scroll": true
    }
}
```

### 配置操作
- **保存配置**：保存当前设置到配置文件
- **加载配置**：从文件加载配置
- **自动保存**：程序关闭时自动保存

## 数据导出

### 支持格式
1. **CSV格式**：表格数据，便于Excel打开
2. **JSON格式**：结构化数据，便于程序处理

### 导出内容
- 变量配置信息
- 当前监控数据
- 导出时间戳

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 确认PLC IP地址正确
   - 验证Rack/Slot参数
   - 检查PLC是否允许外部连接

2. **读取错误**
   - 确认数据块存在
   - 检查偏移地址是否正确
   - 验证数据类型匹配
   - 确保PLC程序正在运行

3. **性能问题**
   - 适当调整监控间隔
   - 减少同时监控的变量数量
   - 检查网络延迟

### 错误代码
- **连接错误**：网络或参数问题
- **读取失败**：数据块或偏移错误
- **格式化失败**：数据类型不匹配

## 技术架构

### 模块结构
```
├── main_monitor.py          # 主程序入口
├── monitor_window.py        # 主窗口界面
├── plc_connection.py        # PLC连接管理
├── variable_monitor.py      # 变量监控线程
├── data_formatter.py        # 数据格式化
├── monitor_config.py        # 配置管理
└── monitor_config.json      # 默认配置文件
```

### 设计特点
- **模块化设计**：功能分离，易于维护
- **多线程架构**：UI和监控分离，响应流畅
- **信号槽机制**：Qt信号槽实现组件通信
- **配置驱动**：JSON配置文件管理设置

## 开发说明

### 扩展功能
- 添加新的数据类型支持
- 实现数据趋势图表
- 增加报警功能
- 支持多PLC连接

### 代码贡献
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

本项目基于原PLC客户端程序重构，保持相同的功能基础并增强监控能力。

## 更新日志

### v1.0.0
- 重构原PLC客户端程序
- 实现专注的变量监控功能
- 支持原始值和十进制值双显示
- 可配置监控间隔
- 完整的配置管理系统
