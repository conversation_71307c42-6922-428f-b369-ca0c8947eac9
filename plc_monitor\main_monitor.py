# -*- coding: utf-8 -*-
"""
PLC变量监控程序主入口
专注于实时监控功能的PLC客户端
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from monitor_window import MonitorWindow


def check_dependencies():
    """检查依赖库"""
    missing_deps = []
    
    try:
        import snap7
    except ImportError:
        missing_deps.append("python-snap7")
    
    try:
        import PySide6
    except ImportError:
        missing_deps.append("PySide6")
    
    if missing_deps:
        error_msg = f"缺少以下依赖库:\n{', '.join(missing_deps)}\n\n请使用以下命令安装:\npip install {' '.join(missing_deps)}"
        print(error_msg)
        return False
    
    return True


def setup_application():
    """设置应用程序"""
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("PLC变量监控器")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("PLC Monitor")
    app.setOrganizationDomain("plc-monitor.local")
    
    # 设置应用程序属性
    app.setAttribute(Qt.ApplicationAttribute.AA_DontCreateNativeWidgetSiblings)
    
    # 使用系统默认样式，不设置自定义样式表
    # 这样可以确保在不同系统和主题下都有良好的显示效果
    
    return app


def main():
    """主函数"""
    print("PLC变量监控器 v1.0.0")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return 1
    
    try:
        # 设置应用程序
        app = setup_application()
        
        # 创建主窗口
        window = MonitorWindow()
        
        # 显示窗口
        window.show()
        
        print("应用程序启动成功")
        print("功能说明:")
        print("1. 连接PLC - 输入IP地址、Rack、Slot参数后点击连接")
        print("2. 添加变量 - 点击'添加变量'按钮配置监控变量")
        print("3. 开始监控 - 连接成功后点击'开始监控'按钮")
        print("4. 实时显示 - 变量值将实时更新，支持原始值和十进制值显示")
        print("5. 配置管理 - 可保存和加载监控配置")
        print("-" * 50)
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        error_msg = f"程序启动失败:\n{str(e)}"
        print(error_msg)
        
        # 如果GUI可用，显示错误对话框
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            QMessageBox.critical(None, "启动错误", error_msg)
        except:
            pass
        
        return 1


if __name__ == "__main__":
    sys.exit(main())
