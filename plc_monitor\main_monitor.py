# -*- coding: utf-8 -*-
"""
PLC变量监控程序主入口
专注于实时监控功能的PLC客户端
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from monitor_window import MonitorWindow


def check_dependencies():
    """检查依赖库"""
    missing_deps = []
    
    try:
        import snap7
    except ImportError:
        missing_deps.append("python-snap7")
    
    try:
        import PySide6
    except ImportError:
        missing_deps.append("PySide6")
    
    if missing_deps:
        error_msg = f"缺少以下依赖库:\n{', '.join(missing_deps)}\n\n请使用以下命令安装:\npip install {' '.join(missing_deps)}"
        print(error_msg)
        return False
    
    return True


def setup_application():
    """设置应用程序"""
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("PLC变量监控器")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("PLC Monitor")
    app.setOrganizationDomain("plc-monitor.local")
    
    # 设置应用程序属性
    app.setAttribute(Qt.ApplicationAttribute.AA_DontCreateNativeWidgetSiblings)
    
    # 设置样式
    app.setStyle("Fusion")
    
    # 设置样式表
    stylesheet = """
    QMainWindow {
        background-color: #f5f5f5;
    }
    
    QGroupBox {
        font-weight: bold;
        border: 2px solid #cccccc;
        border-radius: 5px;
        margin-top: 1ex;
        padding-top: 10px;
    }
    
    QGroupBox::title {
        subcontrol-origin: margin;
        left: 10px;
        padding: 0 5px 0 5px;
    }
    
    QPushButton {
        border: 1px solid #cccccc;
        border-radius: 4px;
        padding: 6px 12px;
        background-color: #ffffff;
        min-width: 80px;
    }
    
    QPushButton:hover {
        background-color: #e6e6e6;
        border-color: #adadad;
    }
    
    QPushButton:pressed {
        background-color: #d4d4d4;
    }
    
    QPushButton:disabled {
        background-color: #f0f0f0;
        color: #999999;
        border-color: #d9d9d9;
    }
    
    QTableWidget {
        gridline-color: #d0d0d0;
        background-color: white;
        alternate-background-color: #f8f8f8;
        selection-background-color: #3daee9;
        border: 1px solid #cccccc;
        border-radius: 3px;
    }
    
    QTableWidget::item {
        padding: 4px;
        border: none;
    }
    
    QTableWidget::item:selected {
        background-color: #3daee9;
        color: white;
    }
    
    QHeaderView::section {
        background-color: #e0e0e0;
        padding: 6px;
        border: 1px solid #cccccc;
        font-weight: bold;
    }
    
    QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
        border: 1px solid #cccccc;
        border-radius: 3px;
        padding: 4px;
        background-color: white;
    }
    
    QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
        border-color: #3daee9;
    }
    
    QCheckBox {
        spacing: 5px;
    }
    
    QCheckBox::indicator {
        width: 16px;
        height: 16px;
    }
    
    QCheckBox::indicator:unchecked {
        border: 1px solid #cccccc;
        background-color: white;
        border-radius: 2px;
    }
    
    QCheckBox::indicator:checked {
        border: 1px solid #3daee9;
        background-color: #3daee9;
        border-radius: 2px;
    }
    
    QStatusBar {
        background-color: #e0e0e0;
        border-top: 1px solid #cccccc;
    }
    
    QSplitter::handle {
        background-color: #cccccc;
    }
    
    QSplitter::handle:horizontal {
        width: 3px;
    }
    
    QSplitter::handle:vertical {
        height: 3px;
    }
    """
    
    app.setStyleSheet(stylesheet)
    
    return app


def main():
    """主函数"""
    print("PLC变量监控器 v1.0.0")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return 1
    
    try:
        # 设置应用程序
        app = setup_application()
        
        # 创建主窗口
        window = MonitorWindow()
        
        # 显示窗口
        window.show()
        
        print("应用程序启动成功")
        print("功能说明:")
        print("1. 连接PLC - 输入IP地址、Rack、Slot参数后点击连接")
        print("2. 添加变量 - 点击'添加变量'按钮配置监控变量")
        print("3. 开始监控 - 连接成功后点击'开始监控'按钮")
        print("4. 实时显示 - 变量值将实时更新，支持原始值和十进制值显示")
        print("5. 配置管理 - 可保存和加载监控配置")
        print("-" * 50)
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        error_msg = f"程序启动失败:\n{str(e)}"
        print(error_msg)
        
        # 如果GUI可用，显示错误对话框
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            QMessageBox.critical(None, "启动错误", error_msg)
        except:
            pass
        
        return 1


if __name__ == "__main__":
    sys.exit(main())
