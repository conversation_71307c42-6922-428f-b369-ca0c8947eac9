# -*- coding: utf-8 -*-
"""
变量编辑对话框
用于编辑现有的监控变量
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QFormLayout, QLineEdit, QSpinBox, 
    QComboBox, QCheckBox, QDialogButtonBox, QLabel, QGroupBox
)
from PySide6.QtCore import Qt
from data_formatter import DataFormatter


class EditVariableDialog(QDialog):
    """编辑变量对话框"""
    
    def __init__(self, variable_data: dict, parent=None):
        super().__init__(parent)
        self.variable_data = variable_data.copy()
        self.setWindowTitle("编辑监控变量")
        self.setModal(True)
        self.resize(400, 350)
        self.setup_ui()
        self.load_variable_data()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 表单布局
        form_layout = QFormLayout()
        
        # 变量名
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("例如: 温度传感器1")
        form_layout.addRow("变量名:", self.name_edit)
        
        # DB号
        self.db_spin = QSpinBox()
        self.db_spin.setRange(1, 999)
        self.db_spin.setValue(1)
        form_layout.addRow("数据块(DB):", self.db_spin)
        
        # 偏移地址
        self.offset_spin = QSpinBox()
        self.offset_spin.setRange(0, 65535)
        self.offset_spin.setValue(0)
        form_layout.addRow("偏移地址:", self.offset_spin)
        
        # 数据类型
        self.type_combo = QComboBox()
        self.type_combo.addItems(["BOOL", "BYTE", "INT", "DINT", "REAL"])
        self.type_combo.setCurrentText("REAL")
        self.type_combo.currentTextChanged.connect(self.on_type_changed)
        form_layout.addRow("数据类型:", self.type_combo)
        
        # 启用状态
        self.enabled_check = QCheckBox("启用监控")
        self.enabled_check.setChecked(True)
        form_layout.addRow("", self.enabled_check)
        
        layout.addLayout(form_layout)
        
        # 类型信息显示
        info_group = QGroupBox("数据类型信息")
        info_layout = QVBoxLayout(info_group)
        
        self.info_label = QLabel()
        self.info_label.setWordWrap(True)
        info_layout.addWidget(self.info_label)
        
        layout.addWidget(info_group)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # 初始化类型信息
        self.on_type_changed()
    
    def load_variable_data(self):
        """加载变量数据到界面"""
        self.name_edit.setText(self.variable_data.get("name", ""))
        self.db_spin.setValue(self.variable_data.get("db", 1))
        self.offset_spin.setValue(self.variable_data.get("offset", 0))
        self.type_combo.setCurrentText(self.variable_data.get("var_type", "REAL"))
        self.enabled_check.setChecked(self.variable_data.get("enabled", True))
    
    def on_type_changed(self):
        """数据类型改变时更新信息"""
        data_type = self.type_combo.currentText()
        type_info = DataFormatter.get_data_type_info(data_type)
        
        info_text = f"""
<b>数据类型:</b> {data_type}<br>
<b>大小:</b> {type_info['size']} 字节<br>
<b>描述:</b> {type_info['description']}<br>
<b>取值范围:</b> {type_info['range']}
        """.strip()
        
        self.info_label.setText(info_text)
    
    def get_variable_data(self) -> dict:
        """获取编辑后的变量数据"""
        return {
            "name": self.name_edit.text().strip(),
            "db": self.db_spin.value(),
            "offset": self.offset_spin.value(),
            "var_type": self.type_combo.currentText(),
            "enabled": self.enabled_check.isChecked()
        }
    
    def validate_input(self) -> tuple[bool, str]:
        """验证输入数据
        
        Returns:
            tuple: (是否有效, 错误信息)
        """
        name = self.name_edit.text().strip()
        if not name:
            return False, "变量名不能为空"
        
        db = self.db_spin.value()
        offset = self.offset_spin.value()
        var_type = self.type_combo.currentText()
        
        # 使用DataFormatter验证
        is_valid, error_msg = DataFormatter.validate_variable_config(db, offset, var_type)
        if not is_valid:
            return False, error_msg
        
        return True, ""
    
    def accept(self):
        """确认按钮处理"""
        is_valid, error_msg = self.validate_input()
        if not is_valid:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "输入错误", error_msg)
            return
        
        super().accept()


class VariableDetailsDialog(QDialog):
    """变量详情查看对话框"""
    
    def __init__(self, variable_data: dict, parent=None):
        super().__init__(parent)
        self.variable_data = variable_data
        self.setWindowTitle(f"变量详情 - {variable_data.get('name', '未知')}")
        self.setModal(True)
        self.resize(500, 400)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)
        
        basic_layout.addRow("变量名:", QLabel(self.variable_data.get("name", "未知")))
        basic_layout.addRow("数据块(DB):", QLabel(str(self.variable_data.get("db", 0))))
        basic_layout.addRow("偏移地址:", QLabel(str(self.variable_data.get("offset", 0))))
        basic_layout.addRow("数据类型:", QLabel(self.variable_data.get("var_type", "未知")))
        basic_layout.addRow("启用状态:", QLabel("是" if self.variable_data.get("enabled", False) else "否"))
        
        layout.addWidget(basic_group)
        
        # 当前值信息组
        value_group = QGroupBox("当前值信息")
        value_layout = QFormLayout(value_group)
        
        raw_data = self.variable_data.get("last_raw_data", "无数据")
        if isinstance(raw_data, (bytes, bytearray)):
            raw_data = ' '.join(f'{b:02X}' for b in raw_data)
        
        value_layout.addRow("原始数据:", QLabel(str(raw_data)))
        value_layout.addRow("十进制值:", QLabel(str(self.variable_data.get("last_value", "无数据"))))
        
        last_update = self.variable_data.get("last_update_time", 0)
        if last_update > 0:
            from datetime import datetime
            update_time = datetime.fromtimestamp(last_update).strftime("%Y-%m-%d %H:%M:%S")
        else:
            update_time = "从未更新"
        value_layout.addRow("最后更新:", QLabel(update_time))
        
        error_count = self.variable_data.get("error_count", 0)
        value_layout.addRow("错误次数:", QLabel(str(error_count)))
        
        layout.addWidget(value_group)
        
        # 数据类型信息组
        type_group = QGroupBox("数据类型信息")
        type_layout = QVBoxLayout(type_group)
        
        var_type = self.variable_data.get("var_type", "REAL")
        type_info = DataFormatter.get_data_type_info(var_type)
        
        info_text = f"""
<b>数据类型:</b> {var_type}<br>
<b>大小:</b> {type_info['size']} 字节<br>
<b>描述:</b> {type_info['description']}<br>
<b>取值范围:</b> {type_info['range']}
        """.strip()
        
        info_label = QLabel(info_text)
        info_label.setWordWrap(True)
        type_layout.addWidget(info_label)
        
        layout.addWidget(type_group)
        
        # 关闭按钮
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
