# -*- coding: utf-8 -*-
"""
PLC监控器测试脚本
用于验证程序模块是否正常工作
"""

import sys
import traceback


def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        # 测试基础依赖
        import PySide6
        print(f"✓ PySide6 版本: {PySide6.__version__}")
        
        import snap7
        print(f"✓ python-snap7 版本: {snap7.__version__}")
        
        # 测试自定义模块
        from plc_connection import PLCConnection
        print("✓ plc_connection 模块导入成功")
        
        from data_formatter import DataFormatter
        print("✓ data_formatter 模块导入成功")
        
        from variable_monitor import VariableMonitor
        print("✓ variable_monitor 模块导入成功")
        
        from monitor_config import MonitorConfig
        print("✓ monitor_config 模块导入成功")
        
        from monitor_window import MonitorWindow
        print("✓ monitor_window 模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        traceback.print_exc()
        return False


def test_data_formatter():
    """测试数据格式化功能"""
    print("\n测试数据格式化...")
    
    try:
        from data_formatter import DataFormatter
        
        # 测试原始数据格式化
        test_data = bytearray([0x01, 0x23, 0x45, 0x67])
        raw_str = DataFormatter.format_raw_data(test_data)
        print(f"✓ 原始数据格式化: {raw_str}")
        
        # 测试十进制值格式化
        decimal_str = DataFormatter.format_decimal_value(123.456, "REAL")
        print(f"✓ 十进制值格式化: {decimal_str}")
        
        # 测试数据类型信息
        type_info = DataFormatter.get_data_type_info("REAL")
        print(f"✓ 数据类型信息: {type_info}")
        
        # 测试变量配置验证
        is_valid, error_msg = DataFormatter.validate_variable_config(1, 0, "REAL")
        print(f"✓ 变量配置验证: {is_valid}, {error_msg}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据格式化测试失败: {e}")
        traceback.print_exc()
        return False


def test_config():
    """测试配置管理"""
    print("\n测试配置管理...")
    
    try:
        from monitor_config import MonitorConfig
        
        # 创建配置对象
        config = MonitorConfig("test_config.json")
        print("✓ 配置对象创建成功")
        
        # 测试配置读取
        conn_config = config.get_connection_config()
        print(f"✓ 连接配置: {conn_config}")
        
        # 测试配置更新
        config.update_connection_config(ip="*************")
        print("✓ 配置更新成功")
        
        # 测试变量管理
        config.add_monitor_variable("测试变量", 1, 0, "REAL")
        variables = config.get_monitor_variables()
        print(f"✓ 变量管理: {len(variables)} 个变量")
        
        # 测试配置验证
        is_valid, errors = config.validate_config()
        print(f"✓ 配置验证: {is_valid}, 错误数: {len(errors)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理测试失败: {e}")
        traceback.print_exc()
        return False


def test_plc_connection():
    """测试PLC连接（不实际连接）"""
    print("\n测试PLC连接模块...")
    
    try:
        from plc_connection import PLCConnection
        
        # 创建连接对象
        plc = PLCConnection()
        print("✓ PLC连接对象创建成功")
        
        # 测试连接状态
        is_connected = plc.is_connected()
        print(f"✓ 连接状态检查: {is_connected}")
        
        # 测试数据解析方法（通过read_variable的内部逻辑）
        print("✓ PLC连接模块基本功能正常")
        
        return True
        
    except Exception as e:
        print(f"✗ PLC连接测试失败: {e}")
        traceback.print_exc()
        return False


def test_gui_creation():
    """测试GUI创建（不显示窗口）"""
    print("\n测试GUI创建...")

    try:
        # 只测试导入，不实际创建GUI对象
        from PySide6.QtWidgets import QApplication
        from monitor_window import MonitorWindow, AddVariableDialog

        print("✓ GUI模块导入成功")
        print("✓ 主窗口类可用")
        print("✓ 对话框类可用")
        print("✓ GUI组件测试完成")

        return True

    except Exception as e:
        print(f"✗ GUI创建测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("PLC监控器模块测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("数据格式化", test_data_formatter),
        ("配置管理", test_config),
        ("PLC连接", test_plc_connection),
        ("GUI创建", test_gui_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✓ {test_name} 测试通过")
        else:
            print(f"✗ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！程序可以正常运行。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
