# -*- mode: python ; coding: utf-8 -*-

"""
西门子S7 PLC客户端程序打包配置（包含snap7.dll）
"""

import sys
import os
from pathlib import Path

# 获取当前目录
current_dir = os.getcwd()

# 数据文件和资源
datas = [
    ('s7_config.json', '.'),  # 配置文件
    ('snap7_lib/snap7.dll', '.'),  # snap7动态链接库
]

# 二进制文件
binaries = [
    ('snap7_lib/snap7.dll', '.'),  # 确保snap7.dll被包含
]

# 隐藏导入
hiddenimports = [
    'PySide6.QtCore',
    'PySide6.QtGui', 
    'PySide6.QtWidgets',
    'snap7',
    'snap7.client',
    'snap7.types',
    'snap7.util',
    'snap7.common',
    'struct',
    'threading',
    'json',
    'datetime',
    'typing',
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'pandas',
    'scipy',
    'PIL',
    'cv2',
    'tensorflow',
    'torch',
    'jupyter',
    'IPython',
]

# 分析主程序
a = Analysis(
    ['main.py'],
    pathex=[current_dir],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 处理PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='S7_PLC_Client_Fixed',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
