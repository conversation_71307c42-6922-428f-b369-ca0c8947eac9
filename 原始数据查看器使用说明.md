# PLC原始数据查看器使用说明

## 🎯 功能概述

原始数据查看器是PLC客户端程序的重要功能，允许用户查看PLC数据块的完整原始二进制数据，帮助深入了解PLC的数据结构和内容。

## 🚀 如何使用

### 1. 启动原始数据查看器

1. **连接PLC**: 首先确保PLC连接成功
2. **执行结构发现**: 点击"发现PLC结构"按钮，完成PLC扫描
3. **打开查看器**: 点击"查看原始数据"按钮（橙色按钮）

### 2. 查看器界面说明

#### 顶部信息面板
- **扫描模式**: 显示使用的扫描模式（智能/摘要/详细）
- **扫描统计**: 显示扫描的DB数量、可访问DB数量等
- **数据概览**: 显示总数据大小和发现的变量数量

#### 数据块选择区域
- **数据块下拉框**: 选择要查看的数据块（如DB1、DB3等）
- **显示格式选择**: 选择数据显示格式
- **刷新按钮**: 刷新数据（提示重新执行发现）

#### 数据显示区域
- **黑色背景文本框**: 显示格式化的原始数据
- **滚动支持**: 支持水平和垂直滚动查看大量数据

## 📊 显示格式详解

### 1. 十六进制格式 (推荐)
```
DB1 原始数据 (十六进制格式)
================================================================================
偏移    00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F    ASCII
--------------------------------------------------------------------------------
0000    <USER> <GROUP> 56 78 9A BC DE F0 11 22 33 44 55 66 77 88    .4Vx...."3DUfw.
0010    99 AA BB CC DD EE FF 00 01 02 03 04 05 06 07 08    ................
```

**特点**:
- 每行显示16个字节
- 左侧显示偏移地址
- 中间显示十六进制值
- 右侧显示对应的ASCII字符
- 最适合查看二进制数据结构

### 2. 十进制格式
```
DB1 原始数据 (十进制格式)
============================================================
偏移    字节值 (十进制)
------------------------------------------------------------
0000     18 (0x12)
0001     52 (0x34)
0002     86 (0x56)
0003    120 (0x78)
```

**特点**:
- 逐字节显示十进制值
- 同时显示对应的十六进制值
- 适合分析数值数据

### 3. 二进制格式
```
DB1 原始数据 (二进制格式)
======================================================================
偏移    二进制值        十六进制  十进制
----------------------------------------------------------------------
0000    <USER>    <GROUP>     18
0001    00110100    0x34     52
0002    01010110    0x56     86
0003    01111000    0x78    120
```

**特点**:
- 显示每个字节的8位二进制表示
- 同时显示十六进制和十进制值
- 适合位级别的数据分析

### 4. ASCII格式
```
DB1 原始数据 (ASCII格式)
============================================================
偏移    ASCII字符    十六进制  十进制
------------------------------------------------------------
0000    '.'          0x12     18
0001    '4'          0x34     52
0002    'V'          0x56     86
0003    'x'          0x78    120
```

**特点**:
- 显示可打印的ASCII字符
- 不可打印字符显示为'.'
- 适合查看文本数据

## 🔧 实用功能

### 1. 数据导出
- 点击"导出原始数据"按钮
- 选择保存位置和文件名
- 导出当前显示格式的数据到文本文件

### 2. 数据刷新
- 点击"刷新数据"按钮
- 系统提示需要重新执行PLC结构发现
- 用于获取最新的PLC数据

### 3. 多数据块切换
- 使用数据块下拉框快速切换
- 显示每个数据块的大小信息
- 自动更新显示内容

## 💡 使用技巧

### 1. 数据分析技巧
- **使用十六进制格式**进行整体数据结构分析
- **使用ASCII格式**查找文本字符串
- **使用二进制格式**进行位操作分析
- **使用十进制格式**分析数值范围

### 2. 数据类型识别
- **字符串数据**: 在ASCII格式中查找连续的可打印字符
- **整数数据**: 在十六进制格式中查找规律性的字节模式
- **浮点数据**: 通常占用4字节，需要IEEE 754格式解析
- **布尔数据**: 通常为0x00或0x01

### 3. 偏移地址计算
- 偏移地址从0开始计算
- 每行十六进制格式显示16字节
- 可以根据偏移地址定位具体的数据位置

## ⚠️ 注意事项

1. **数据实时性**: 显示的是发现时刻的数据快照，不是实时数据
2. **数据大小限制**: 大数据块可能需要滚动查看
3. **格式选择**: 根据数据类型选择合适的显示格式
4. **导出功能**: 导出的是当前选择的格式和数据块

## 🔍 故障排除

### 问题1: 无法打开原始数据查看器
**解决方案**: 确保已经执行过PLC结构发现

### 问题2: 显示"无可访问的数据块"
**解决方案**: 检查PLC连接状态，重新执行结构发现

### 问题3: 数据显示不完整
**解决方案**: 使用滚动条查看完整数据，或选择合适的显示格式

## 📈 高级应用

### 1. 数据结构逆向工程
通过原始数据查看器，可以分析PLC程序的数据结构，了解变量的存储布局。

### 2. 数据完整性验证
对比不同时间点的原始数据，验证数据的一致性和变化。

### 3. 协议分析
分析PLC通信协议的数据格式和编码方式。

---

**提示**: 原始数据查看器是高级功能，建议结合PLC编程手册和数据类型规范使用，以获得最佳的分析效果。
