# PLC监控器样式修复说明

## 问题描述
用户反馈界面显示问题：界面是白色的，文字也是白色的，导致无法看清内容。

## 问题原因
程序中设置了自定义的CSS样式表，可能在某些系统环境或主题下导致颜色冲突，使得文字和背景颜色相同。

## 修复方案
移除所有自定义样式设置，改为使用系统默认样式，确保在不同环境下都有良好的显示效果。

## 修复内容

### 1. main_monitor.py
- **修复前**: 设置了复杂的自定义样式表，包括背景色、边框、按钮样式等
- **修复后**: 移除所有自定义样式，使用系统默认样式

```python
# 修复前
app.setStyle("Fusion")
app.setStyleSheet(stylesheet)  # 大量自定义CSS

# 修复后  
# 使用系统默认样式，不设置自定义样式表
# 这样可以确保在不同系统和主题下都有良好的显示效果
```

### 2. monitor_window.py
移除了以下组件的自定义样式设置：

- **信息标签**: 移除背景色和边框样式
- **连接按钮**: 移除背景色和文字颜色设置
- **连接状态标签**: 移除文字颜色设置
- **监控按钮**: 移除背景色和文字颜色设置
- **状态变化时的样式**: 移除动态样式变更

```python
# 修复前
self.connect_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px 16px; }")
self.connection_status.setStyleSheet("color: red; font-weight: bold;")

# 修复后
# 移除自定义样式，使用默认样式
```

## 修复效果

### 优势
1. **兼容性更好**: 适应不同操作系统和主题
2. **显示稳定**: 避免颜色冲突问题
3. **维护简单**: 减少样式相关的问题
4. **用户体验**: 遵循系统界面规范，用户更熟悉

### 功能保持
- 所有核心功能完全保持不变
- PLC连接功能正常
- 实时监控功能正常
- 配置管理功能正常
- 数据显示功能正常

## 测试验证
修复后通过了完整的模块测试：
- ✅ 模块导入测试
- ✅ 数据格式化测试  
- ✅ 配置管理测试
- ✅ PLC连接测试
- ✅ GUI创建测试

## 使用建议

### 启动程序
```bash
# 进入程序目录
cd plc_monitor

# 启动程序
python main_monitor.py

# 或使用批处理文件（Windows）
start_monitor.bat
```

### 如果仍有显示问题
1. **检查系统主题**: 确保使用标准的系统主题
2. **更新显卡驱动**: 确保显卡驱动是最新版本
3. **检查DPI设置**: 确保系统DPI设置正常
4. **重启程序**: 有时需要重启程序才能生效

### 自定义样式（可选）
如果需要自定义外观，建议：
1. 使用Qt Designer设计界面
2. 只设置必要的样式属性
3. 测试在不同主题下的显示效果
4. 提供样式开关选项

## 总结
通过移除自定义样式，程序现在使用系统默认样式，确保在各种环境下都能正常显示。这是一个更稳定和兼容的解决方案。
