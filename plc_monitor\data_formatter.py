# -*- coding: utf-8 -*-
"""
数据格式化模块
提供原始值和十进制值的格式化显示功能
"""

from typing import Optional, Any


class DataFormatter:
    """数据格式化类"""
    
    @staticmethod
    def format_raw_data(data: bytearray) -> str:
        """格式化原始字节数据为十六进制字符串
        
        Args:
            data: 原始字节数据
            
        Returns:
            str: 十六进制格式的字符串，如 "0x01 0x23"
        """
        if not data:
            return "无数据"
        
        hex_bytes = [f"0x{byte:02X}" for byte in data]
        return " ".join(hex_bytes)
    
    @staticmethod
    def format_decimal_value(value: Any, var_type: str) -> str:
        """格式化十进制值
        
        Args:
            value: 解析后的值
            var_type: 变量类型
            
        Returns:
            str: 格式化后的十进制值字符串
        """
        if value is None:
            return "读取失败"
        
        var_type = var_type.upper()
        
        try:
            if var_type == "BOOL":
                return "True" if value else "False"
            
            elif var_type == "BYTE":
                return f"{value} (0x{value:02X})"
            
            elif var_type == "INT":
                return f"{value}"
            
            elif var_type == "DINT":
                return f"{value}"
            
            elif var_type == "REAL":
                # 浮点数保留6位小数
                return f"{value:.6f}"
            
            else:
                return str(value)
                
        except Exception:
            return "格式化失败"
    
    @staticmethod
    def format_binary_data(data: bytearray) -> str:
        """格式化为二进制字符串
        
        Args:
            data: 原始字节数据
            
        Returns:
            str: 二进制格式的字符串
        """
        if not data:
            return "无数据"
        
        binary_bytes = [f"{byte:08b}" for byte in data]
        return " ".join(binary_bytes)
    
    @staticmethod
    def get_data_type_info(var_type: str) -> dict:
        """获取数据类型信息
        
        Args:
            var_type: 变量类型
            
        Returns:
            dict: 包含类型信息的字典
        """
        type_info = {
            "BOOL": {
                "size": 1,
                "description": "布尔值 (True/False)",
                "range": "True 或 False"
            },
            "BYTE": {
                "size": 1,
                "description": "8位无符号整数",
                "range": "0 - 255"
            },
            "INT": {
                "size": 2,
                "description": "16位有符号整数",
                "range": "-32,768 - 32,767"
            },
            "DINT": {
                "size": 4,
                "description": "32位有符号整数",
                "range": "-2,147,483,648 - 2,147,483,647"
            },
            "REAL": {
                "size": 4,
                "description": "32位IEEE 754浮点数",
                "range": "±1.175e-38 - ±3.403e+38"
            }
        }
        
        return type_info.get(var_type.upper(), {
            "size": 1,
            "description": "未知类型",
            "range": "未知"
        })
    
    @staticmethod
    def validate_variable_config(db: int, offset: int, var_type: str) -> tuple[bool, str]:
        """验证变量配置
        
        Args:
            db: 数据块号
            offset: 偏移地址
            var_type: 变量类型
            
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        # 检查DB号
        if not (1 <= db <= 999):
            return False, "DB号必须在1-999之间"
        
        # 检查偏移地址
        if offset < 0:
            return False, "偏移地址不能为负数"
        
        # 检查数据类型
        valid_types = ["BOOL", "BYTE", "INT", "DINT", "REAL"]
        if var_type.upper() not in valid_types:
            return False, f"不支持的数据类型: {var_type}，支持的类型: {', '.join(valid_types)}"
        
        # 检查偏移地址对齐
        type_info = DataFormatter.get_data_type_info(var_type)
        size = type_info["size"]
        
        if var_type.upper() in ["INT", "DINT", "REAL"] and offset % 2 != 0:
            return False, f"{var_type}类型需要偶数偏移地址（字对齐）"
        
        if var_type.upper() in ["DINT", "REAL"] and offset % 4 != 0:
            return False, f"{var_type}类型建议4字节对齐的偏移地址"
        
        return True, ""
    
    @staticmethod
    def format_timestamp(timestamp: float) -> str:
        """格式化时间戳
        
        Args:
            timestamp: Unix时间戳
            
        Returns:
            str: 格式化的时间字符串
        """
        import datetime
        try:
            dt = datetime.datetime.fromtimestamp(timestamp)
            return dt.strftime("%H:%M:%S.%f")[:-3]  # 精确到毫秒
        except:
            return "无效时间"
    
    @staticmethod
    def get_value_change_indicator(old_value: Any, new_value: Any) -> str:
        """获取值变化指示器
        
        Args:
            old_value: 旧值
            new_value: 新值
            
        Returns:
            str: 变化指示器 ("↑", "↓", "=", "?")
        """
        if old_value is None or new_value is None:
            return "?"
        
        try:
            if isinstance(old_value, (int, float)) and isinstance(new_value, (int, float)):
                if new_value > old_value:
                    return "↑"
                elif new_value < old_value:
                    return "↓"
                else:
                    return "="
            else:
                if str(new_value) != str(old_value):
                    return "≠"
                else:
                    return "="
        except:
            return "?"
