========================================
西门子S7 PLC客户端程序 v1.0 (修复版)
========================================

📋 系统要求
- Windows 10/11 操作系统
- 无需安装Python或其他依赖

🚀 快速开始
1. 双击运行 S7_PLC_Client.exe
2. 在左侧面板配置PLC连接参数
3. 点击"连接"按钮连接到PLC
4. 连接成功后可以开始使用各项功能

⚙️ 配置说明
- 修改 s7_config.json 文件来配置默认PLC参数
- 默认配置：IP=************, Rack=0, Slot=1

🔧 主要功能
1. PLC连接管理
   - 自动连接/断开
   - 连接状态监控

2. PLC结构发现
   - 自动扫描数据块
   - 智能变量识别
   - 三种扫描模式（智能/摘要/详细）

3. 实时数据监控
   - 变量值实时更新
   - 可配置监控间隔
   - 数据变化高亮显示

4. 原始数据查看器
   - 完整的原始二进制数据显示
   - 多种显示格式（十六进制/十进制/二进制/ASCII）
   - 数据导出功能
   - 系统主题色界面

5. 变量读写
   - 支持多种数据类型
   - 实时读取和写入

📁 文件说明
- S7_PLC_Client.exe: 主程序文件（包含snap7.dll）
- s7_config.json: PLC连接配置文件
- README.md: 详细技术文档
- 使用说明.txt: 本文件

🔍 故障排除
1. 程序无法启动
   - 检查是否被防病毒软件阻止
   - 尝试以管理员身份运行
   - 确保Windows系统完整性

2. 无法连接PLC
   - 检查网络连接
   - 确认PLC IP地址和参数
   - 检查防火墙设置
   - 确保PLC处于RUN状态

3. 发现功能无响应
   - 确保PLC连接正常
   - 尝试不同的扫描模式
   - 检查PLC数据块访问权限

4. 监控数据不更新
   - 检查PLC连接状态
   - 确认变量地址正确
   - 调整监控间隔

5. 原始数据查看器问题
   - 先执行PLC结构发现
   - 确保有可访问的数据块

📞 技术支持
如遇到问题，请检查：
1. PLC网络连接是否正常
2. 配置文件是否正确
3. 防火墙是否允许程序访问网络
4. Windows系统是否支持

⚠️ 注意事项
- 首次启动可能需要几秒钟
- 确保PLC处于RUN状态
- 建议在测试环境中先验证功能
- 写入操作请谨慎使用
- 本版本已包含snap7.dll，无需额外安装

🆕 修复版本更新内容
- ✅ 修复启动失败问题
- ✅ 包含snap7.dll动态链接库
- ✅ 优化扫描信息框界面（系统主题色）
- ✅ 增加实时监控发现变量功能
- ✅ 完善原始数据查看器

📝 版本信息
版本: 1.0.0 (修复版)
构建日期: 2024年7月29日
文件大小: 约52MB
修复内容: 包含snap7.dll，解决启动和运行问题

========================================
© 2024 PLC Solutions. All rights reserved.
========================================
