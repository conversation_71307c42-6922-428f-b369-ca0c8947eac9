# -*- coding: utf-8 -*-
"""
PLC变量监控主窗口
专注于实时监控功能的用户界面
"""

import time
from typing import Dict, Any
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QGroupBox, QLabel, QLineEdit, QPushButton, QSpinBox, QDoubleSpinBox,
    QTableWidget, QTableWidgetItem, QComboBox, QCheckBox,
    QSplitter, QMessageBox, QStatusBar, QHeaderView,
    QDialog, QDialogButtonBox, QFormLayout, QFileDialog
)
from PySide6.QtCore import Qt, QTimer, Slot
from PySide6.QtGui import QColor

from plc_connection import PLCConnection
from variable_monitor import VariableMonitor
from data_formatter import DataFormatter
from monitor_config import MonitorConfig


class AddVariableDialog(QDialog):
    """添加变量对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加监控变量")
        self.setModal(True)
        self.resize(400, 300)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 表单布局
        form_layout = QFormLayout()
        
        # 变量名
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("例如: 温度传感器1")
        form_layout.addRow("变量名:", self.name_edit)
        
        # DB号
        self.db_spin = QSpinBox()
        self.db_spin.setRange(1, 999)
        self.db_spin.setValue(1)
        form_layout.addRow("数据块(DB):", self.db_spin)
        
        # 偏移地址
        self.offset_spin = QSpinBox()
        self.offset_spin.setRange(0, 65535)
        self.offset_spin.setValue(0)
        form_layout.addRow("偏移地址:", self.offset_spin)
        
        # 数据类型
        self.type_combo = QComboBox()
        self.type_combo.addItems(["BOOL", "BYTE", "INT", "DINT", "REAL"])
        self.type_combo.setCurrentText("REAL")
        form_layout.addRow("数据类型:", self.type_combo)
        
        # 启用状态
        self.enabled_check = QCheckBox()
        self.enabled_check.setChecked(True)
        form_layout.addRow("启用监控:", self.enabled_check)
        
        layout.addLayout(form_layout)
        
        # 类型信息显示
        self.info_label = QLabel()
        self.info_label.setWordWrap(True)
        # 移除自定义样式，使用默认样式
        layout.addWidget(self.info_label)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # 连接信号
        self.type_combo.currentTextChanged.connect(self.update_type_info)
        self.update_type_info()
    
    def update_type_info(self):
        """更新类型信息"""
        var_type = self.type_combo.currentText()
        type_info = DataFormatter.get_data_type_info(var_type)
        
        info_text = f"""
数据类型: {var_type}
大小: {type_info['size']} 字节
描述: {type_info['description']}
取值范围: {type_info['range']}
        """.strip()
        
        self.info_label.setText(info_text)
    
    def get_variable_config(self) -> Dict[str, Any]:
        """获取变量配置"""
        return {
            "name": self.name_edit.text().strip(),
            "db": self.db_spin.value(),
            "offset": self.offset_spin.value(),
            "type": self.type_combo.currentText(),
            "enabled": self.enabled_check.isChecked()
        }


class MonitorWindow(QMainWindow):
    """主监控窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PLC变量实时监控器")
        self.setMinimumSize(1200, 800)
        
        # 初始化组件
        self.config = MonitorConfig()
        self.plc_connection = PLCConnection()
        self.variable_monitor = VariableMonitor(self.plc_connection)
        
        # 数据存储
        self.variable_data: Dict[str, Dict] = {}  # 存储变量的历史数据
        
        # 设置UI
        self.setup_ui()
        self.setup_connections()
        self.setup_timer()
        self.load_config()
        
        # 状态变量
        self.monitoring = False
    
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 顶部连接区域
        connection_group = self.create_connection_group()
        main_layout.addWidget(connection_group)
        
        # 中间分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        control_panel = self.create_control_panel()
        splitter.addWidget(control_panel)
        
        # 右侧数据显示
        data_panel = self.create_data_panel()
        splitter.addWidget(data_panel)
        
        # 设置分割器比例
        splitter.setSizes([350, 850])
        
        # 状态栏
        self.setup_status_bar()
    
    def create_connection_group(self) -> QGroupBox:
        """创建连接配置组"""
        group = QGroupBox("PLC连接")
        layout = QHBoxLayout(group)
        
        # IP地址
        layout.addWidget(QLabel("IP地址:"))
        self.ip_edit = QLineEdit()
        self.ip_edit.setPlaceholderText("************")
        layout.addWidget(self.ip_edit)
        
        # Rack
        layout.addWidget(QLabel("Rack:"))
        self.rack_spin = QSpinBox()
        self.rack_spin.setRange(0, 7)
        layout.addWidget(self.rack_spin)
        
        # Slot
        layout.addWidget(QLabel("Slot:"))
        self.slot_spin = QSpinBox()
        self.slot_spin.setRange(0, 31)
        layout.addWidget(self.slot_spin)
        
        # 连接按钮
        self.connect_btn = QPushButton("连接")
        # 移除自定义样式，使用默认样式
        layout.addWidget(self.connect_btn)

        # 连接状态
        self.connection_status = QLabel("未连接")
        # 移除自定义样式，使用默认样式
        layout.addWidget(self.connection_status)
        
        layout.addStretch()
        
        return group
    
    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 监控控制组
        monitor_group = QGroupBox("监控控制")
        monitor_layout = QVBoxLayout(monitor_group)
        
        # 监控间隔
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("监控间隔(秒):"))
        self.interval_spin = QDoubleSpinBox()
        self.interval_spin.setRange(0.1, 10.0)
        self.interval_spin.setValue(1.0)
        self.interval_spin.setSingleStep(0.1)
        self.interval_spin.setDecimals(1)
        interval_layout.addWidget(self.interval_spin)
        monitor_layout.addLayout(interval_layout)
        
        # 监控按钮
        self.monitor_btn = QPushButton("开始监控")
        self.monitor_btn.setEnabled(False)
        # 移除自定义样式，使用默认样式
        monitor_layout.addWidget(self.monitor_btn)
        
        layout.addWidget(monitor_group)
        
        # 变量管理组
        variable_group = QGroupBox("变量管理")
        variable_layout = QVBoxLayout(variable_group)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        self.add_var_btn = QPushButton("添加变量")
        self.add_var_btn.clicked.connect(self.add_variable)
        button_layout.addWidget(self.add_var_btn)
        
        self.remove_var_btn = QPushButton("移除变量")
        self.remove_var_btn.clicked.connect(self.remove_variable)
        button_layout.addWidget(self.remove_var_btn)
        
        variable_layout.addLayout(button_layout)
        
        # 配置按钮
        config_layout = QHBoxLayout()
        
        self.save_config_btn = QPushButton("保存配置")
        self.save_config_btn.clicked.connect(self.save_configuration)
        config_layout.addWidget(self.save_config_btn)
        
        self.load_config_btn = QPushButton("加载配置")
        self.load_config_btn.clicked.connect(self.load_configuration)
        config_layout.addWidget(self.load_config_btn)
        
        variable_layout.addLayout(config_layout)
        
        layout.addWidget(variable_group)
        
        # 显示选项组
        display_group = QGroupBox("显示选项")
        display_layout = QVBoxLayout(display_group)
        
        self.show_binary_check = QCheckBox("显示二进制")
        display_layout.addWidget(self.show_binary_check)
        
        self.highlight_changes_check = QCheckBox("高亮变化")
        self.highlight_changes_check.setChecked(True)
        display_layout.addWidget(self.highlight_changes_check)
        
        self.auto_scroll_check = QCheckBox("自动滚动")
        self.auto_scroll_check.setChecked(True)
        display_layout.addWidget(self.auto_scroll_check)
        
        layout.addWidget(display_group)
        
        layout.addStretch()
        
        return panel

    def create_data_panel(self) -> QWidget:
        """创建数据显示面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # 数据表格
        self.data_table = QTableWidget()
        self.data_table.setColumnCount(8)
        headers = ["变量名", "DB", "偏移", "类型", "原始值", "十进制值", "变化", "更新时间"]
        self.data_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.data_table.setAlternatingRowColors(True)
        self.data_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.data_table.setSortingEnabled(True)

        # 设置列宽
        header = self.data_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 变量名
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)  # 原始值
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)  # 十进制值

        # 设置固定列宽
        self.data_table.setColumnWidth(0, 120)  # 变量名
        self.data_table.setColumnWidth(1, 60)   # DB
        self.data_table.setColumnWidth(2, 80)   # 偏移
        self.data_table.setColumnWidth(3, 80)   # 类型
        self.data_table.setColumnWidth(6, 60)   # 变化
        self.data_table.setColumnWidth(7, 120)  # 更新时间

        layout.addWidget(self.data_table)

        # 底部操作按钮
        button_layout = QHBoxLayout()

        self.clear_data_btn = QPushButton("清空数据")
        self.clear_data_btn.clicked.connect(self.clear_data)
        button_layout.addWidget(self.clear_data_btn)

        self.export_data_btn = QPushButton("导出数据")
        self.export_data_btn.clicked.connect(self.export_data)
        button_layout.addWidget(self.export_data_btn)

        button_layout.addStretch()

        # 统计信息
        self.stats_label = QLabel("监控变量: 0 | 活跃: 0 | 错误: 0")
        button_layout.addWidget(self.stats_label)

        layout.addLayout(button_layout)

        return panel

    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 连接状态
        self.conn_status_label = QLabel("PLC: 未连接")
        self.status_bar.addWidget(self.conn_status_label)

        self.status_bar.addWidget(QLabel(" | "))

        # 监控状态
        self.monitor_status_label = QLabel("监控: 停止")
        self.status_bar.addWidget(self.monitor_status_label)

        self.status_bar.addWidget(QLabel(" | "))

        # 更新频率
        self.update_rate_label = QLabel("更新率: 0 Hz")
        self.status_bar.addPermanentWidget(self.update_rate_label)

    def setup_connections(self):
        """设置信号连接"""
        # 连接按钮
        self.connect_btn.clicked.connect(self.toggle_connection)

        # 监控按钮
        self.monitor_btn.clicked.connect(self.toggle_monitoring)

        # 监控间隔变化
        self.interval_spin.valueChanged.connect(self.on_interval_changed)

        # PLC连接信号
        self.plc_connection.connection_changed.connect(self.on_connection_changed)
        self.plc_connection.error_occurred.connect(self.on_plc_error)

        # 变量监控信号
        self.variable_monitor.variable_updated.connect(self.on_variable_updated)
        self.variable_monitor.monitor_error.connect(self.on_monitor_error)
        self.variable_monitor.monitor_status_changed.connect(self.on_monitor_status_changed)

    def setup_timer(self):
        """设置定时器"""
        # UI更新定时器
        self.ui_timer = QTimer()
        self.ui_timer.timeout.connect(self.update_ui)
        self.ui_timer.start(100)  # 100ms更新一次

        # 统计更新定时器
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_statistics)
        self.stats_timer.start(1000)  # 1秒更新一次统计

    def load_config(self):
        """加载配置"""
        # 加载连接配置
        conn_config = self.config.get_connection_config()
        self.ip_edit.setText(conn_config.get("ip", "************"))
        self.rack_spin.setValue(conn_config.get("rack", 0))
        self.slot_spin.setValue(conn_config.get("slot", 1))

        # 加载监控配置
        monitor_config = self.config.get_monitor_config()
        self.interval_spin.setValue(monitor_config.get("interval", 1.0))

        # 加载UI配置
        ui_config = self.config.get_ui_config()
        self.show_binary_check.setChecked(ui_config.get("show_binary", False))
        self.highlight_changes_check.setChecked(ui_config.get("highlight_changes", True))
        self.auto_scroll_check.setChecked(ui_config.get("auto_scroll", True))

        # 加载监控变量
        self.load_monitor_variables()

        # 自动连接
        if conn_config.get("auto_connect", False):
            QTimer.singleShot(1000, self.connect_plc)  # 延迟1秒自动连接

    def load_monitor_variables(self):
        """加载监控变量"""
        variables = self.config.get_monitor_variables()

        # 清空现有变量
        self.variable_monitor.clear_variables()
        self.data_table.setRowCount(0)
        self.variable_data.clear()

        # 添加配置中的变量
        for var_config in variables:
            name = var_config.get("name", "")
            db = var_config.get("db", 1)
            offset = var_config.get("offset", 0)
            var_type = var_config.get("type", "REAL")
            enabled = var_config.get("enabled", True)

            if name:
                success = self.variable_monitor.add_variable(name, db, offset, var_type)
                if success:
                    self.variable_monitor.set_variable_enabled(name, enabled)
                    self.add_variable_to_table(name, db, offset, var_type, enabled)

    def add_variable_to_table(self, name: str, db: int, offset: int, var_type: str, enabled: bool = True):
        """添加变量到表格"""
        row = self.data_table.rowCount()
        self.data_table.insertRow(row)

        # 设置表格项
        self.data_table.setItem(row, 0, QTableWidgetItem(name))
        self.data_table.setItem(row, 1, QTableWidgetItem(str(db)))
        self.data_table.setItem(row, 2, QTableWidgetItem(str(offset)))
        self.data_table.setItem(row, 3, QTableWidgetItem(var_type))
        self.data_table.setItem(row, 4, QTableWidgetItem("--"))
        self.data_table.setItem(row, 5, QTableWidgetItem("--"))
        self.data_table.setItem(row, 6, QTableWidgetItem("--"))
        self.data_table.setItem(row, 7, QTableWidgetItem("--"))

        # 设置行颜色（如果禁用）
        if not enabled:
            for col in range(8):
                item = self.data_table.item(row, col)
                if item:
                    item.setBackground(QColor(240, 240, 240))

        # 初始化变量数据
        self.variable_data[name] = {
            "last_raw_data": None,
            "last_value": None,
            "last_update_time": 0,
            "update_count": 0
        }

    @Slot()
    def toggle_connection(self):
        """切换连接状态"""
        if self.plc_connection.is_connected():
            self.disconnect_plc()
        else:
            self.connect_plc()

    def connect_plc(self):
        """连接PLC"""
        ip = self.ip_edit.text().strip()
        rack = self.rack_spin.value()
        slot = self.slot_spin.value()

        if not ip:
            QMessageBox.warning(self, "警告", "请输入PLC IP地址")
            return

        # 更新配置
        self.config.update_connection_config(ip=ip, rack=rack, slot=slot)

        # 连接PLC
        success = self.plc_connection.connect(ip, rack, slot)
        if not success:
            QMessageBox.critical(self, "错误", "PLC连接失败，请检查网络和参数设置")

    def disconnect_plc(self):
        """断开PLC连接"""
        # 先停止监控
        if self.monitoring:
            self.stop_monitoring()

        # 断开连接
        self.plc_connection.disconnect()

    @Slot()
    def toggle_monitoring(self):
        """切换监控状态"""
        if self.monitoring:
            self.stop_monitoring()
        else:
            self.start_monitoring()

    def start_monitoring(self):
        """开始监控"""
        if not self.plc_connection.is_connected():
            QMessageBox.warning(self, "警告", "请先连接PLC")
            return

        if self.data_table.rowCount() == 0:
            QMessageBox.warning(self, "警告", "请先添加监控变量")
            return

        # 设置监控间隔
        interval = self.interval_spin.value()
        self.variable_monitor.set_interval(interval)

        # 开始监控
        self.variable_monitor.start_monitoring()

        # 更新配置
        self.config.update_monitor_config(interval=interval)

    def stop_monitoring(self):
        """停止监控"""
        self.variable_monitor.stop_monitoring()

    @Slot(float)
    def on_interval_changed(self, interval: float):
        """监控间隔变化"""
        if self.monitoring:
            self.variable_monitor.set_interval(interval)
        self.config.update_monitor_config(interval=interval)

    @Slot(bool)
    def on_connection_changed(self, connected: bool):
        """连接状态变化处理"""
        if connected:
            self.connection_status.setText("已连接")
            # 移除自定义样式，使用默认样式
            self.connect_btn.setText("断开")
            # 移除自定义样式，使用默认样式
            self.monitor_btn.setEnabled(True)
            self.conn_status_label.setText("PLC: 已连接")
        else:
            self.connection_status.setText("未连接")
            # 移除自定义样式，使用默认样式
            self.connect_btn.setText("连接")
            # 移除自定义样式，使用默认样式
            self.monitor_btn.setEnabled(False)
            self.conn_status_label.setText("PLC: 未连接")

            # 如果正在监控，停止监控
            if self.monitoring:
                self.stop_monitoring()

    @Slot(str)
    def on_plc_error(self, error_msg: str):
        """PLC错误处理"""
        print(f"PLC错误: {error_msg}")
        self.status_bar.showMessage(f"PLC错误: {error_msg}", 5000)

    @Slot(str, bytearray, object, float)
    def on_variable_updated(self, var_name: str, raw_data: bytearray, value: Any, timestamp: float):
        """变量更新处理"""
        # 查找表格行
        row = self.find_variable_row(var_name)
        if row == -1:
            return

        # 获取变量类型
        type_item = self.data_table.item(row, 3)
        if not type_item:
            return
        var_type = type_item.text()

        # 格式化数据
        raw_str = DataFormatter.format_raw_data(raw_data)
        decimal_str = DataFormatter.format_decimal_value(value, var_type)
        time_str = DataFormatter.format_timestamp(timestamp)

        # 获取变化指示器
        old_data = self.variable_data.get(var_name, {})
        old_value = old_data.get("last_value")
        change_indicator = DataFormatter.get_value_change_indicator(old_value, value)

        # 更新表格
        self.data_table.setItem(row, 4, QTableWidgetItem(raw_str))
        self.data_table.setItem(row, 5, QTableWidgetItem(decimal_str))
        self.data_table.setItem(row, 6, QTableWidgetItem(change_indicator))
        self.data_table.setItem(row, 7, QTableWidgetItem(time_str))

        # 高亮变化
        if self.highlight_changes_check.isChecked() and old_value is not None and value != old_value:
            for col in range(4, 8):
                item = self.data_table.item(row, col)
                if item:
                    item.setBackground(QColor(255, 255, 0, 100))  # 淡黄色高亮
                    # 1秒后恢复正常颜色
                    QTimer.singleShot(1000, lambda r=row, c=col: self.reset_item_color(r, c))

        # 更新变量数据
        self.variable_data[var_name] = {
            "last_raw_data": raw_data,
            "last_value": value,
            "last_update_time": timestamp,
            "update_count": old_data.get("update_count", 0) + 1
        }

        # 自动滚动到更新的行
        if self.auto_scroll_check.isChecked():
            item = self.data_table.item(row, 0)
            if item:
                self.data_table.scrollToItem(item)

    def reset_item_color(self, row: int, col: int):
        """重置表格项颜色"""
        try:
            item = self.data_table.item(row, col)
            if item:
                item.setBackground(QColor())  # 恢复默认背景
        except:
            pass

    def find_variable_row(self, var_name: str) -> int:
        """查找变量在表格中的行号"""
        for row in range(self.data_table.rowCount()):
            item = self.data_table.item(row, 0)
            if item and item.text() == var_name:
                return row
        return -1

    @Slot(str, str)
    def on_monitor_error(self, var_name: str, error_msg: str):
        """监控错误处理"""
        print(f"监控错误 [{var_name}]: {error_msg}")

        # 在表格中显示错误
        row = self.find_variable_row(var_name)
        if row != -1:
            self.data_table.setItem(row, 5, QTableWidgetItem(f"错误: {error_msg}"))
            # 设置错误颜色
            item = self.data_table.item(row, 5)
            if item:
                item.setBackground(QColor(255, 200, 200))  # 淡红色

    @Slot(bool)
    def on_monitor_status_changed(self, monitoring: bool):
        """监控状态变化处理"""
        self.monitoring = monitoring

        if monitoring:
            self.monitor_btn.setText("停止监控")
            # 移除自定义样式，使用默认样式
            self.monitor_status_label.setText("监控: 运行中")
        else:
            self.monitor_btn.setText("开始监控")
            # 移除自定义样式，使用默认样式
            self.monitor_status_label.setText("监控: 停止")

    @Slot()
    def add_variable(self):
        """添加监控变量"""
        dialog = AddVariableDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            config = dialog.get_variable_config()
            name = config["name"]

            if not name:
                QMessageBox.warning(self, "警告", "变量名不能为空")
                return

            # 检查变量名是否已存在
            if self.find_variable_row(name) != -1:
                QMessageBox.warning(self, "警告", f"变量名 '{name}' 已存在")
                return

            # 添加到监控器
            success = self.variable_monitor.add_variable(
                name, config["db"], config["offset"], config["type"]
            )

            if success:
                # 添加到表格
                self.add_variable_to_table(
                    name, config["db"], config["offset"], config["type"], config["enabled"]
                )

                # 设置启用状态
                self.variable_monitor.set_variable_enabled(name, config["enabled"])

                # 保存到配置
                self.config.add_monitor_variable(
                    name, config["db"], config["offset"], config["type"], config["enabled"]
                )

                print(f"添加变量成功: {name}")
            else:
                QMessageBox.critical(self, "错误", f"添加变量失败: {name}")

    @Slot()
    def remove_variable(self):
        """移除监控变量"""
        current_row = self.data_table.currentRow()
        if current_row == -1:
            QMessageBox.warning(self, "警告", "请选择要移除的变量")
            return

        # 获取变量名
        name_item = self.data_table.item(current_row, 0)
        if not name_item:
            return

        var_name = name_item.text()

        # 确认删除
        reply = QMessageBox.question(
            self, "确认", f"确定要移除变量 '{var_name}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 从监控器移除
            self.variable_monitor.remove_variable(var_name)

            # 从表格移除
            self.data_table.removeRow(current_row)

            # 从数据存储移除
            if var_name in self.variable_data:
                del self.variable_data[var_name]

            # 从配置移除
            self.config.remove_monitor_variable(var_name)

            print(f"移除变量: {var_name}")

    @Slot()
    def clear_data(self):
        """清空数据显示"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空所有数据显示吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 清空表格数据列
            for row in range(self.data_table.rowCount()):
                for col in range(4, 8):  # 只清空数据列
                    self.data_table.setItem(row, col, QTableWidgetItem("--"))

            # 清空变量数据
            for var_name in self.variable_data:
                self.variable_data[var_name] = {
                    "last_raw_data": None,
                    "last_value": None,
                    "last_update_time": 0,
                    "update_count": 0
                }

            print("数据显示已清空")

    @Slot()
    def export_data(self):
        """导出数据"""
        if self.data_table.rowCount() == 0:
            QMessageBox.warning(self, "警告", "没有数据可导出")
            return

        # 选择导出文件
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出数据", "monitor_data.csv", "CSV文件 (*.csv);;JSON文件 (*.json)"
        )

        if not file_path:
            return

        try:
            if file_path.endswith('.json'):
                self.export_to_json(file_path)
            else:
                self.export_to_csv(file_path)

            QMessageBox.information(self, "成功", f"数据已导出到:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败:\n{str(e)}")

    def export_to_csv(self, file_path: str):
        """导出为CSV格式"""
        import csv

        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # 写入表头
            headers = []
            for col in range(self.data_table.columnCount()):
                header_item = self.data_table.horizontalHeaderItem(col)
                headers.append(header_item.text() if header_item else f"列{col}")
            writer.writerow(headers)

            # 写入数据
            for row in range(self.data_table.rowCount()):
                row_data = []
                for col in range(self.data_table.columnCount()):
                    item = self.data_table.item(row, col)
                    row_data.append(item.text() if item else "")
                writer.writerow(row_data)

    def export_to_json(self, file_path: str):
        """导出为JSON格式"""
        import json

        data = {
            "export_time": time.time(),
            "variables": []
        }

        for row in range(self.data_table.rowCount()):
            var_data = {}
            for col in range(self.data_table.columnCount()):
                header_item = self.data_table.horizontalHeaderItem(col)
                header = header_item.text() if header_item else f"列{col}"

                item = self.data_table.item(row, col)
                var_data[header] = item.text() if item else ""

            data["variables"].append(var_data)

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)

    @Slot()
    def save_configuration(self):
        """保存配置"""
        # 更新当前UI状态到配置
        self.update_config_from_ui()

        # 保存配置文件
        if self.config.save_config():
            self.status_bar.showMessage("配置已保存", 2000)
        else:
            QMessageBox.critical(self, "错误", "保存配置失败")

    @Slot()
    def load_configuration(self):
        """加载配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "加载配置", "", "JSON文件 (*.json)"
        )

        if not file_path:
            return

        try:
            if self.config.import_config(file_path):
                # 重新加载配置
                self.load_config()
                self.status_bar.showMessage("配置已加载", 2000)
            else:
                QMessageBox.critical(self, "错误", "加载配置失败")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载配置失败:\n{str(e)}")

    def update_config_from_ui(self):
        """从UI更新配置"""
        # 更新连接配置
        self.config.update_connection_config(
            ip=self.ip_edit.text().strip(),
            rack=self.rack_spin.value(),
            slot=self.slot_spin.value()
        )

        # 更新监控配置
        self.config.update_monitor_config(
            interval=self.interval_spin.value()
        )

        # 更新UI配置
        self.config.update_ui_config(
            show_binary=self.show_binary_check.isChecked(),
            highlight_changes=self.highlight_changes_check.isChecked(),
            auto_scroll=self.auto_scroll_check.isChecked()
        )

        # 更新变量配置
        variables = []
        for row in range(self.data_table.rowCount()):
            name_item = self.data_table.item(row, 0)
            db_item = self.data_table.item(row, 1)
            offset_item = self.data_table.item(row, 2)
            type_item = self.data_table.item(row, 3)

            if name_item and db_item and offset_item and type_item:
                try:
                    variables.append({
                        "name": name_item.text(),
                        "db": int(db_item.text()),
                        "offset": int(offset_item.text()),
                        "type": type_item.text(),
                        "enabled": True  # 默认启用
                    })
                except (ValueError, AttributeError):
                    continue  # 跳过无效的行

        self.config.set_monitor_variables(variables)

    @Slot()
    def update_ui(self):
        """更新UI显示"""
        # 这里可以添加定期UI更新逻辑
        pass

    @Slot()
    def update_statistics(self):
        """更新统计信息"""
        # 获取监控状态
        status = self.variable_monitor.get_monitor_status()

        # 更新统计标签
        stats_text = (
            f"监控变量: {status['total_variables']} | "
            f"活跃: {status['enabled_variables']} | "
            f"错误: {status['error_variables']}"
        )
        self.stats_label.setText(stats_text)

        # 计算更新频率
        if self.monitoring:
            total_updates = sum(
                data.get("update_count", 0)
                for data in self.variable_data.values()
            )
            # 简单的更新率计算（每秒更新次数）
            update_rate = total_updates / max(1, time.time() - getattr(self, '_start_time', time.time()))
            self.update_rate_label.setText(f"更新率: {update_rate:.1f} Hz")
        else:
            self.update_rate_label.setText("更新率: 0 Hz")

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止监控
        if self.monitoring:
            self.stop_monitoring()

        # 断开PLC连接
        if self.plc_connection.is_connected():
            self.plc_connection.disconnect()

        # 保存配置
        self.update_config_from_ui()
        self.config.save_config()

        # 记录开始时间用于计算更新率
        if not hasattr(self, '_start_time'):
            self._start_time = time.time()

        event.accept()
