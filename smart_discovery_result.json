{"data_blocks": {"1": {"size": 4, "accessible": true, "data": "bytearray(b'\\x01\\x00\\x04\\xd2')", "scan_time": 1753697928.5310528}, "2": {"size": 0, "accessible": false, "data": null, "error": "b'CPU : Address out of range'"}, "3": {"size": 12, "accessible": true, "data": "bytearray(b'\\x00\\x01\\x00\\x00\\x00\\x0b\\x00\\x00\\x00\\x00\\x00\\x00')", "scan_time": 1753697928.5607884}, "4": {"size": 0, "accessible": false, "data": null, "error": "b'CPU : Address out of range'"}, "5": {"size": 0, "accessible": false, "data": null, "error": "b'CPU : Address out of range'"}, "6": {"size": 12, "accessible": true, "data": "bytearray(b'\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\xf0\\x03\\x02\\x00')", "scan_time": 1753697928.5961802}, "7": {"size": 0, "accessible": false, "data": null, "error": "b'CPU : Address out of range'"}, "8": {"size": 70, "accessible": true, "data": "bytearray(b'\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\n\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x10~\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x06\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00')", "scan_time": 1753697928.6212578}, "9": {"size": 0, "accessible": false, "data": null, "error": "b'CPU : Address out of range'"}, "10": {"size": 0, "accessible": false, "data": null, "error": "b'CPU : Address out of range'"}}, "variables": [{"name": "DB1_INT_0", "db": 1, "offset": 0, "type": "INT", "value": 256, "description": "DB1偏移0的整数值"}, {"name": "DB1_INT_2", "db": 1, "offset": 2, "type": "INT", "value": 1234, "description": "DB1偏移2的整数值"}, {"name": "DB3_DINT_0", "db": 3, "offset": 0, "type": "DINT", "value": 65536, "description": "DB3偏移0的长整数值"}, {"name": "DB3_DINT_4", "db": 3, "offset": 4, "type": "DINT", "value": 720896, "description": "DB3偏移4的长整数值"}, {"name": "DB3_DINT_8", "db": 3, "offset": 8, "type": "DINT", "value": 0, "description": "DB3偏移8的长整数值"}, {"name": "DB6_DINT_0", "db": 6, "offset": 0, "type": "DINT", "value": 0, "description": "DB6偏移0的长整数值"}, {"name": "DB6_DINT_4", "db": 6, "offset": 4, "type": "DINT", "value": 0, "description": "DB6偏移4的长整数值"}, {"name": "DB6_INT_8", "db": 6, "offset": 8, "type": "INT", "value": -4093, "description": "DB6偏移8的整数值"}, {"name": "DB6_INT_10", "db": 6, "offset": 10, "type": "INT", "value": 512, "description": "DB6偏移10的整数值"}, {"name": "DB8_DINT_0", "db": 8, "offset": 0, "type": "DINT", "value": 0, "description": "DB8偏移0的长整数值"}, {"name": "DB8_DINT_4", "db": 8, "offset": 4, "type": "DINT", "value": 10, "description": "DB8偏移4的长整数值"}, {"name": "DB8_DINT_8", "db": 8, "offset": 8, "type": "DINT", "value": 0, "description": "DB8偏移8的长整数值"}, {"name": "DB8_DINT_12", "db": 8, "offset": 12, "type": "DINT", "value": 0, "description": "DB8偏移12的长整数值"}, {"name": "DB8_DINT_16", "db": 8, "offset": 16, "type": "DINT", "value": 0, "description": "DB8偏移16的长整数值"}, {"name": "DB8_DINT_20", "db": 8, "offset": 20, "type": "DINT", "value": 0, "description": "DB8偏移20的长整数值"}, {"name": "DB8_DINT_24", "db": 8, "offset": 24, "type": "DINT", "value": 0, "description": "DB8偏移24的长整数值"}, {"name": "DB8_DINT_28", "db": 8, "offset": 28, "type": "DINT", "value": 0, "description": "DB8偏移28的长整数值"}, {"name": "DB8_DINT_32", "db": 8, "offset": 32, "type": "DINT", "value": 0, "description": "DB8偏移32的长整数值"}, {"name": "DB8_DINT_36", "db": 8, "offset": 36, "type": "DINT", "value": 0, "description": "DB8偏移36的长整数值"}, {"name": "DB8_DINT_40", "db": 8, "offset": 40, "type": "DINT", "value": 0, "description": "DB8偏移40的长整数值"}, {"name": "DB8_INT_44", "db": 8, "offset": 44, "type": "INT", "value": 4222, "description": "DB8偏移44的整数值"}, {"name": "DB8_INT_46", "db": 8, "offset": 46, "type": "INT", "value": 0, "description": "DB8偏移46的整数值"}, {"name": "DB8_DINT_48", "db": 8, "offset": 48, "type": "DINT", "value": 0, "description": "DB8偏移48的长整数值"}, {"name": "DB8_DINT_52", "db": 8, "offset": 52, "type": "DINT", "value": 393216, "description": "DB8偏移52的长整数值"}, {"name": "DB8_DINT_56", "db": 8, "offset": 56, "type": "DINT", "value": 0, "description": "DB8偏移56的长整数值"}, {"name": "DB8_DINT_60", "db": 8, "offset": 60, "type": "DINT", "value": 0, "description": "DB8偏移60的长整数值"}, {"name": "DB8_DINT_64", "db": 8, "offset": 64, "type": "DINT", "value": 0, "description": "DB8偏移64的长整数值"}, {"name": "DB8_INT_68", "db": 8, "offset": 68, "type": "INT", "value": 0, "description": "DB8偏移68的整数值"}], "summary": {"scan_mode": "smart", "total_dbs_scanned": 10, "accessible_dbs": 4, "accessible_db_numbers": [1, 3, 6, 8], "total_data_size": 98, "total_variables_found": 28, "variable_types": {"INT": 7, "DINT": 21}}}